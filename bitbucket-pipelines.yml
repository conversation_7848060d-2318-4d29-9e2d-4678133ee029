image: atlassian/default-image:2

definitions:
  steps:
    # Placeholder test step
    - step: &test
        name: "Test"
        script:
          - echo "Nenhum teste a ser executado..."

    # Deploy step with variable validation
    - step: &deploy
        name: "Deploy"
        script:
          - echo "Validando variáveis de deploy..."
          - |
            for VAR in SSH_PRIVATE_KEY SSH_BASTION_KEY BASTION_HOST BASTION_USER \
                        DEPLOY_SSH_HOST DEPLOY_SSH_USER DEPLOY_PATH PHP_BINARY RSYNC_DELETE; do
              if [ -z "${!VAR}" ]; then
                echo "ERRO: Variável $VAR não configurada!"
                exit 1
              fi
            done

          - echo "Configurando chaves SSH"
          - mkdir -p ~/.ssh

          - export SSH_START_TOKEN="-----BEGIN RSA PRIVATE KEY-----"
          - export SSH_END_TOKEN="-----END RSA PRIVATE KEY-----"
          - printf "%s\n%s\n%s" "$SSH_START_TOKEN" "$SSH_PRIVATE_KEY" "$SSH_END_TOKEN" > ~/.ssh/id_rsa
          - printf "%s\n%s\n%s" "$SSH_START_TOKEN" "$SSH_BASTION_KEY" "$SSH_END_TOKEN" > ~/.ssh/bastion_key

          - chmod 600 ~/.ssh/id_rsa ~/.ssh/bastion_key

          - ssh-keyscan -t rsa bitbucket.org >> ~/.ssh/known_hosts

          - |
            cat > ~/.ssh/config <<EOF
            Host deploy_server
              HostName ${DEPLOY_SSH_HOST}
              User ${DEPLOY_SSH_USER}
              Port 22
              IdentityFile ~/.ssh/id_rsa
              StrictHostKeyChecking no
              ProxyCommand ssh -o StrictHostKeyChecking=no \
                -i ~/.ssh/bastion_key \
                -W %h:%p ${BASTION_USER}@${BASTION_HOST}
            EOF

          - echo "Configurando opções do rsync"
          - |
            RSYNC_FLAGS="-rzc --force --progress --owner --group --exclude-from=.deploy_exclude"
            if [ "${RSYNC_DELETE}" = "true" ]; then
              RSYNC_FLAGS="$RSYNC_FLAGS --delete"
              echo "⚠️ Arquivos remotos serão removidos (exceto config.php) ⚠️"
            fi

          - echo "Executando deploy para ${DEPLOY_PATH}"
          - rsync $RSYNC_FLAGS ./ deploy_server:${DEPLOY_PATH}

          - echo "Executando script de upgrade"
          - ssh deploy_server "cd ${DEPLOY_PATH} && ${PHP_BINARY} admin/cli/upgrade.php --non-interactive"

pipelines:
  branches:
    develop:
      # - step: *test
      - step:
          <<: *deploy
          name: "Deploy para Staging"
          deployment: staging

    master:
      # - step: *test
      - step:
          <<: *deploy
          name: "Deploy para Production"
          deployment: production