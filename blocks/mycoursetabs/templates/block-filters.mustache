{{#filters}}
<form>
	<div class="d-flex flex-column flex-md-row align-items-stretch align-items-md-end justify-content-end flex-wrap">
		<div class="d-flex align-items-center mr-auto">
			

			{{#recommended}}
				{{#enabled}}
				<div class="text-left mr-3 mb-2 mb-md-0">
					<div class="btn-group-toggle" data-toggle="buttons">
						<label class="btn btn-outline px-0">
							<input id="recommended" type="checkbox" name="recommended" value="{{value}}" {{#checked}}checked{{/checked}}> 
							<i class="fa-solid fa-toggle-off fa-fw fa-xl"></i> Indicados para mim
						</label>
					</div>
				</div>
				{{/enabled}}
			{{/recommended}}
            
            {{#most_accessed}}
				{{#enabled}}
				<div class="text-left mr-3 mb-2 mb-md-0">
					<div class="btn-group-toggle" data-toggle="buttons">
						<label class="btn btn-outline px-0">
							<input type="checkbox" name="most_accessed" value="{{value}}" {{#checked}}checked{{/checked}}> 
							<i class="fa-solid fa-toggle-off fa-fw fa-xl"></i> Mais Acessados
						</label>
					</div>
				</div>
				{{/enabled}}
			{{/most_accessed}}

            {{#favourite}}
				{{#enabled}}
				<div class="text-left mx-3 mb-2 mb-md-0">
					<div class="btn-group-toggle" data-toggle="buttons">
						<label class="btn btn-outline px-0">
							<input type="checkbox" name="favourite" value="{{value}}" {{#checked}}checked{{/checked}}> 
							<i class="fa-solid fa-toggle-off fa-fw fa-xl"></i> Favoritas
						</label>
					</div>
				</div>
				{{/enabled}}
			{{/favourite}}
		</div>

		{{#status}}
			{{#enabled}}
			<div class="text-left mr-0 mr-md-2 mb-2 mb-md-0">
				<select class="custom-select rounded-sm w-100" id="input-status" name="status">
					<option value="">Status de Matrícula</option>
					{{#options}}
					<option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
					{{/options}}
				</select>
			</div>
			{{/enabled}}
		{{/status}}

        {{#status_progress}}
			{{#enabled}}
			<div class="text-left mr-0 mr-md-2 mb-2 mb-md-0">
				<select class="custom-select rounded-sm w-100" id="input-status-progress" name="status_progress">
					<option value="-1">Status de Progresso</option>
					{{#options}}
					<option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
					{{/options}}
				</select>
			</div>
			{{/enabled}}
		{{/status_progress}}

        {{#categoryid}}
			{{#enabled}}
				<div class="text-left mr-2" style="max-width: 180px;">
					<select class="custom-select rounded-sm w-100" id="input-tematica" name="categoryid">
						<option value="">Grupo/ Temática</option>
						{{#options}}
						<option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
						{{/options}}
					</select>
				</div>
			{{/enabled}}
		{{/categoryid}}

		{{#level}}
			{{#enabled}}
			<div class="text-left mr-0 mr-md-2 mb-2 mb-md-0">
				<select class="custom-select rounded-sm w-100" id="input-level" name="level">
					<option value="">Nível ocupacional</option>
					{{#options}}
					<option value="{{name}}" {{#selected}}selected{{/selected}}>{{name}}</option>
					{{/options}}
				</select>
			</div>
			{{/enabled}}
		{{/level}}
		
		<div class="d-flex align-items-center justify-content-between mr-md-2 mb-2 mb-md-0">
            {{#modality}}
			    {{#enabled}}
                    <div class="text-left mr-0 mr-md-2 mb-2 mb-md-0">
                        <select class="custom-select rounded-sm w-100" id="input-modality" name="modality">
                            <option value="">Formato de Solução</option>
                            {{#options}}
                            <option value="{{name}}" {{#selected}}selected{{/selected}}>{{name}}</option>
                            {{/options}}
                        </select>
                    </div>
                {{/enabled}}
            {{/modality}}
			{{#uf}}
				{{#enabled}}
				<div class="text-left">
					<select class="custom-select rounded-sm w-100" id="input-uf" name="uf">
						<option value="">UF</option>
						{{#options}}
						<option value="{{name}}" {{#selected}}selected{{/selected}}>{{name}}</option>
						{{/options}}
					</select>
				</div>
				{{/enabled}}
			{{/uf}}
		</div>

        <div class="flex-grow-1 mr-md-2 mb-2 rounded-sm search" style="max-width: 350px;">
            <div class="input-group rounded-sm">
                <input type="search" class="form-control rounded-sm" name="search" placeholder="Buscar..." value="{{search}}">
                <div class="input-group-append rounded-sm">
                    <button class="btn btn-outline-secondary" type="submit" aria-label="Buscar">
                        <i class="fa fa-magnifying-glass" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        </div>

		<div class="mb-auto">
			<a href="#" class="btn btn-info btn-clear-filters">
				<i class="fa-solid fa-filter-circle-xmark pr-1"></i>
				Limpar Filtros
			</a>
		</div>
	</div>
</form>
{{/filters}}