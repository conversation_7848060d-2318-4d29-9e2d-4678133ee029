{"version": 3, "file": "block.min.js", "sources": ["../src/block.js"], "sourcesContent": ["import Ajax from 'core/ajax';\r\nimport Templates from 'core/templates';\r\nimport * as Notification from \"core/notification\";\r\nimport API from \"local_courseblockapi/api\";\r\n\r\nconst SELECTORS = {\r\n\tblock_container: \".mycoursetabs\",\r\n\tfilters: \".mycoursetabs .filters\",\r\n\tfilters_input: \".mycoursetabs .filters input, .mycoursetabs .filters select\",\r\n\tfilters_recommended: \".mycoursetabs .filters input[name='recommended']\",\r\n\tpagination: \".mycoursetabs .pagination-wrapper\",\r\n\tfooter_container: \".mycoursetabs .courses-page-footer\",\r\n}\r\n\r\nasync function get_courses(tab = \"mycourses\", args = {}) {\r\n\tlet method = {\r\n\t\tmycourses: \"block_mycoursetabs_get_my_courses\",\r\n\t\tallcourses: \"block_mycoursetabs_get_all_courses\",\r\n\t}\r\n\r\n\tconst request = {\r\n\t\tmethodname: method[tab],\r\n\t\targs: args,\r\n\t};\r\n\r\n\treturn Ajax.call([request])[0];\r\n}\r\n\r\nasync function get_tab_filters(tab = \"mycourses\", filtered = {}) {\r\n\tconst request = {\r\n\t\tmethodname: \"block_mycoursetabs_get_filters\",\r\n\t\targs: {\r\n\t\t\ttab: tab,\r\n\t\t\tfiltered: filtered,\r\n\t\t},\r\n\t};\r\n\r\n\treturn Ajax.call([request])[0];\r\n}\r\n\r\nasync function render_courses(tab, args = { filters: {} }) {\r\n\tlet $loading = $(SELECTORS.block_container + \" .smart-loading\");\r\n\tlet $currentTabCards = $(SELECTORS.block_container + \" .tab-pane#\" + tab + \" .cards\");\r\n\tlet $otherTabsCards = $(SELECTORS.block_container + \" .tab-pane:not(#\" + tab + \") .cards\");\r\n\r\n\t$loading.addClass(\"show\");\r\n\r\n\tget_courses(tab, args)\r\n\t\t.then(async (data) => {\r\n\t\t\tconsole.log(data);\r\n\t\t\tconsole.log(data.query);\r\n\t\t\tconsole.log(data.filters);\r\n\t\t\tlet paginationData = prepare_page_pagination(data);\r\n            console.log(\"paginationData: \");\r\n            console.log(paginationData);\r\n\t\t\t// Tratar isso no backend futuramente\r\n\t\t\t$.each(data.items, function () {\r\n\t\t\t\tthis.display_coursename = this.display_coursename && [\"Sim\", \"Yes\"].includes(this.display_coursename) || false;\r\n\t\t\t})\r\n\r\n\t\t\tlet cards = await Templates.render(\"block_mycoursetabs/block-cards\", data);\r\n\t\t\tlet pagination = paginationData ? await Templates.render(\"block_mycoursetabs/pagination\", paginationData) : \"\";\r\n\r\n\t\t\t$currentTabCards.html(cards);\r\n\t\t\t$(SELECTORS.pagination).html(pagination);\r\n\r\n\t\t\t$loading.removeClass(\"show\");\r\n\t\t\t$otherTabsCards.html(\"\");\r\n\t\t\treset_cards_height();\r\n\t\t})\r\n\t\t.catch(error => {\r\n\t\t\tconsole.error(error);\r\n\t\t\tNotification.exception();\r\n\t\t})\r\n}\r\n\r\nasync function render_filters(tab, filtered = {}) {\r\n\tget_tab_filters(tab, filtered)\r\n\t\t.then(async (data) => {\r\n\t\t\tlet filters = await Templates.render(\"block_mycoursetabs/block-filters\", { filters: data });\r\n\t\t\t$(SELECTORS.filters).html(filters);\r\n\t\t});\r\n}\r\n\r\nexport const init = async function () {\r\n\t// Carrega a aba selecionada padrão\r\n\tconsole.log(\"Initializing My Course Tabs block...\");\r\n\tlet tab = get_current_tab();\r\n\tconsole.log(tab);\r\n\trender_courses(tab);\r\n\trender_filters(tab);\r\n\r\n\t$(document).on(\"click\", SELECTORS.block_container + \" .tablink\", function (e) {\r\n\t\tlet tab = $(this).data(\"target\").replace(\"#\", \"\");\r\n\t\t$(SELECTORS.pagination).html(\"\");\r\n\r\n\t\trender_courses(tab);\r\n\t\trender_filters(tab);\r\n\t})\r\n\t\t.on(\"change\", SELECTORS.filters_input, function () {\r\n\t\t\t$(this).closest(\"form\").submit();\r\n\t\t})\r\n\t\t.on(\"click\", SELECTORS.block_container + \" .btn-clear-filters\", function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tlet tab = get_current_tab();\r\n\r\n\t\t\t$(SELECTORS.filters_input).val(\"\");\r\n\t\t\t$(SELECTORS.filters_input).filter(\":checked\").prop(\"checked\", false);\r\n\t\t\t$(SELECTORS.filters_input).filter(\"#recommended\").prop(\"checked\", true);\r\n\r\n\t\t\t//$(SELECTORS.filters_recommended).prop(\"checked\", true);\r\n\r\n\t\t\trender_courses(tab);\r\n\t\t})\r\n\t\t.on(\"submit\", SELECTORS.block_container + \" .filters form\", function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tlet tab = get_current_tab();\r\n\t\t\tlet filters = get_filters_data();\r\n\r\n\t\t\trender_courses(tab, { filters: filters });\r\n\t\t})\r\n\t\t.on(\"click\", SELECTORS.pagination + \" .page-link\", function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tlet tab = get_current_tab();\r\n\t\t\tlet filters = get_filters_data();\r\n\t\t\tlet currentPage = get_current_page();\r\n\t\t\tlet pageValue = $(this).data(\"page\");\r\n\t\t\tlet page = pageValue;\r\n\r\n\t\t\trender_courses(tab, { page: page, filters: filters });\r\n\t\t})\r\n\t\t.on(\"click\", \".card-course-favourite\", function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tlet $button = $(this);\r\n\t\t\tlet courseid = $button.data(\"courseid\");\r\n\t\t\tlet status = $button.data(\"favourite\") == 0 || $button.data(\"favourite\") == false;\r\n\r\n\t\t\tAPI.set_favourite({\r\n\t\t\t\tcourses: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcomponent: null, //null means it favourites in system context\r\n\t\t\t\t\t\tid: courseid,\r\n\t\t\t\t\t\tfavourite: status,\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t})\r\n\t\t\t\t.then((result) => {\r\n\t\t\t\t\tif (result.warnings) {\r\n\t\t\t\t\t\tresult.warnings.forEach((warning) => {\r\n\t\t\t\t\t\t\tNotification.addNotification({\r\n\t\t\t\t\t\t\t\tmessage: warning.message,\r\n\t\t\t\t\t\t\t\ttype: \"error\",\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet tab = get_current_tab();\r\n\t\t\t\t\tlet filters = get_filters_data();\r\n\t\t\t\t\tlet page = get_current_page();\r\n\t\t\t\t\tlet toggle = status ? 1 : 0;\r\n\r\n\t\t\t\t\t$button.data(\"favourite\", toggle);\r\n\t\t\t\t\t$button.find(\"i\").toggleClass(\"fa-regular\", !toggle);\r\n\t\t\t\t\t$button.find(\"i\").toggleClass(\"fa\", toggle);\r\n\r\n\t\t\t\t\trender_courses(tab, { page: page, filters: filters });\r\n\t\t\t\t})\r\n\t\t})\r\n\r\n\t$(window).on(\"resize\", reset_cards_height);\r\n}\r\n\r\nfunction get_current_page() {\r\n    return $(SELECTORS.pagination + \" .page-item.active button.page-link\").data(\"page\") || 1;\r\n}\r\n\r\nconst prepare_page_pagination = (results) => {\r\n    \r\n    console.log(\"prepare_page_pagination results: \");\r\n    console.log(results);\r\n\r\n    if (!results?.total_pages || results.total_pages == 1) {\r\n        return {};\r\n    }\r\n\r\n    let isMobileView = window.innerWidth <= 600/*  */\r\n    let pagination = {\r\n        label: \"Page\",\r\n        pages: [],\r\n        haspages: true,\r\n        pagesize: results.per_page || 10,\r\n    };\r\n\r\n    let currentPage = results.page;\r\n    let totalPages = results.total_pages;\r\n\r\n    if (currentPage > 1) {\r\n        pagination.previous = { page: currentPage - 1, url: `?page=${currentPage - 1}` };\r\n    }\r\n\r\n    if (currentPage < totalPages) {\r\n        pagination.next = { page: currentPage + 1, url: `?page=${currentPage + 1}` };\r\n    }\r\n\r\n    let treshold = 0;\r\n    if (totalPages <= 5) {\r\n        for (let i = 1; i <= totalPages; i++) {\r\n            pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n        }\r\n    }\r\n    else {\r\n\r\n        if ((currentPage <= 5 && !isMobileView) || (currentPage <= 3 && isMobileView)) {\r\n            // primeiras páginas\r\n            if (currentPage <= totalPages - 5) {\r\n                treshold = 2;\r\n            }\r\n\r\n            if (!isMobileView) {\r\n                for (let i = 1; i <= currentPage + 3 && i <= totalPages - treshold; i++) {\r\n                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n                }\r\n            }\r\n            else {\r\n                for (let i = 1; i <= 3; i++) {\r\n                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n                }\r\n            }\r\n\r\n            if (treshold == 2 || isMobileView) {\r\n                pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };\r\n            }\r\n            else if (currentPage + 3 < totalPages) {\r\n                pagination.pages.push({ page: totalPages, active: totalPages === currentPage, url: `?page=${totalPages}` });\r\n            }\r\n\r\n        } else if ((currentPage > totalPages - 5 && !isMobileView) || (currentPage > totalPages - 3 && isMobileView)) {\r\n            // últimas páginas\r\n            treshold = 1;\r\n            if (isMobileView) {\r\n                if (currentPage > 3) {\r\n                    treshold = totalPages - 2;\r\n                    pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n                }\r\n            }\r\n            else if (currentPage >= 6) {\r\n                treshold = currentPage - 3;\r\n                pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n            }\r\n\r\n            for (let i = treshold; i <= totalPages; i++) {\r\n                pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n            }\r\n        } else {\r\n            // meio\r\n            pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n            if (isMobileView) {\r\n                pagination.pages.push({ page: currentPage, active: true, url: `?page=${currentPage}` });\r\n            }\r\n            else {\r\n                for (let i = currentPage - 3; i <= currentPage + 3; i++) {\r\n                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n                }\r\n            }\r\n            pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };\r\n        }\r\n    }\r\n    return pagination;\r\n};\r\n\r\nfunction get_filters_data() {\r\n\tlet filtersData = $(SELECTORS.block_container + \" .filters form\").serializeArray();\r\n\tlet filters = {};\r\n\r\n\t$.each(filtersData, function () {\r\n\t\tif (this.value) {\r\n\t\t\tfilters[this.name] = this.value;\r\n\t\t}\r\n\t});\r\n\r\n\treturn filters;\r\n};\r\n\r\nfunction get_current_tab() {\r\n\tlet $activeTab = $(SELECTORS.block_container + \" .tablink.active\");\r\n\tconsole.log(\"active Tab: \");\r\n\tconsole.log($activeTab);\r\n\treturn $activeTab.data(\"target\").replace(\"#\", \"\");\r\n}\r\n\r\nfunction reset_cards_height() {\r\n\tconst imgOriginalWidth = 600;\r\n\tconst imgOriginalHeight = 350;\r\n\tconst cardSelector = SELECTORS.block_container + \" .card\";\r\n\tconst container = document.querySelector(SELECTORS.block_container);\r\n\r\n\tconst cards = document.querySelectorAll(cardSelector);\r\n\tif (!cards.length) return false;\r\n\r\n\tconst cardWidths = Array.from(cards).map((card) => card.scrollWidth);\r\n\tconst card_width = Math.max(...cardWidths);\r\n\r\n\tconst new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;\r\n\tconst new_card_height = new_image_height + 16; // add 16px\r\n\r\n\tcontainer.style.setProperty(\"--img-height\", `${new_image_height}px`);\r\n\tcontainer.style.setProperty(\"--card-height\", `${new_card_height}px`);\r\n\r\n\tconst cardBodySelector = cardSelector + \" .card-body\";\r\n\tconst cardBodies = document.querySelectorAll(cardBodySelector);\r\n\r\n\tconst cardBodyHeights = Array.from(cardBodies).map(\r\n\t\t(body) => body.scrollHeight\r\n\t);\r\n\tconst card_body_height = Math.max(...cardBodyHeights);\r\n\r\n\tconst new_card_height_hover = card_body_height + new_image_height + 5;\r\n\tcontainer.style.setProperty(\"--card-height-hover\", `${new_card_height_hover}px`);\r\n\r\n\treturn true;\r\n};\r\n"], "names": ["SELECTORS", "block_container", "filters", "filters_input", "filters_recommended", "pagination", "footer_container", "render_courses", "tab", "args", "$loading", "$", "$currentTabCards", "$otherTabsCards", "addClass", "request", "methodname", "mycourses", "allcourses", "Ajax", "call", "get_courses", "then", "async", "console", "log", "data", "query", "paginationData", "prepare_page_pagination", "each", "items", "display_coursename", "this", "includes", "cards", "Templates", "render", "html", "removeClass", "reset_cards_height", "catch", "error", "Notification", "exception", "render_filters", "filtered", "get_tab_filters", "get_current_page", "get_current_tab", "document", "on", "e", "replace", "closest", "submit", "preventDefault", "val", "filter", "prop", "get_filters_data", "page", "$button", "courseid", "status", "set_favourite", "courses", "component", "id", "favourite", "result", "warnings", "for<PERSON>ach", "warning", "addNotification", "message", "type", "toggle", "find", "toggleClass", "window", "results", "total_pages", "isMobile<PERSON>iew", "innerWidth", "label", "pages", "haspages", "pagesize", "per_page", "currentPage", "totalPages", "previous", "url", "next", "treshold", "i", "push", "active", "last", "first", "filtersData", "serializeArray", "value", "name", "$activeTab", "cardSelector", "container", "querySelector", "querySelectorAll", "length", "cardWidths", "Array", "from", "map", "card", "scrollWidth", "new_image_height", "Math", "max", "new_card_height", "style", "setProperty", "cardBodySelector", "cardBodies", "cardBodyHeights", "body", "scrollHeight", "new_card_height_hover"], "mappings": "q4CAKMA,UAAY,CACjBC,gBAAiB,gBACjBC,QAAS,yBACTC,cAAe,8DACfC,oBAAqB,mDACrBC,WAAY,oCACZC,iBAAkB,qDA6BJC,eAAeC,SAAKC,4DAAO,CAAEP,QAAS,IAChDQ,SAAWC,EAAEX,UAAUC,gBAAkB,mBACzCW,iBAAmBD,EAAEX,UAAUC,gBAAkB,cAAgBO,IAAM,WACvEK,gBAAkBF,EAAEX,UAAUC,gBAAkB,mBAAqBO,IAAM,YAE/EE,SAASI,SAAS,+BAzBZC,QAAU,CACfC,WANY,CACZC,UAAW,oCACXC,WAAY,6FAHmB,aAQ/BT,4DARmD,WAW7CU,cAAKC,KAAK,CAACL,UAAU,GAsB5BM,CAAYb,IAAKC,MACfa,MAAKC,aACLC,QAAQC,IAAIC,MACZF,QAAQC,IAAIC,KAAKC,OACjBH,QAAQC,IAAIC,KAAKxB,aACb0B,eAAiBC,wBAAwBH,MACpCF,QAAQC,IAAI,oBACZD,QAAQC,IAAIG,gBAErBjB,EAAEmB,KAAKJ,KAAKK,OAAO,gBACbC,mBAAqBC,KAAKD,oBAAsB,CAAC,MAAO,OAAOE,SAASD,KAAKD,sBAAuB,SAGtGG,YAAcC,mBAAUC,OAAO,iCAAkCX,MACjErB,WAAauB,qBAAuBQ,mBAAUC,OAAO,gCAAiCT,gBAAkB,GAE5GhB,iBAAiB0B,KAAKH,OACtBxB,EAAEX,UAAUK,YAAYiC,KAAKjC,YAE7BK,SAAS6B,YAAY,QACrB1B,gBAAgByB,KAAK,IACrBE,wBAEAC,OAAMC,QACNlB,QAAQkB,MAAMA,OACdC,aAAaC,8BAIDC,eAAerC,6BA/CvBO,QAAU,CACfC,WAAY,iCACZP,KAAM,CACLD,2DAJkC,YAKlCsC,gEAL0D,YASrD3B,cAAKC,KAAK,CAACL,UAAU,IAwC5BgC,CAAgBvC,2DAD6B,IAE3Cc,MAAKC,iBACDrB,cAAgBkC,mBAAUC,OAAO,mCAAoC,CAAEnC,QAASwB,OACpFf,EAAEX,UAAUE,SAASoC,KAAKpC,qBAkGpB8C,0BACErC,EAAEX,UAAUK,WAAa,uCAAuCqB,KAAK,SAAW,gBA/FvEH,iBAEnBC,QAAQC,IAAI,4CACRjB,IAAMyC,kBACVzB,QAAQC,IAAIjB,KACZD,eAAeC,KACfqC,eAAerC,KAEfG,EAAEuC,UAAUC,GAAG,QAASnD,UAAUC,gBAAkB,aAAa,SAAUmD,OACtE5C,IAAMG,EAAEsB,MAAMP,KAAK,UAAU2B,QAAQ,IAAK,IAC9C1C,EAAEX,UAAUK,YAAYiC,KAAK,IAE7B/B,eAAeC,KACfqC,eAAerC,QAEd2C,GAAG,SAAUnD,UAAUG,eAAe,WACtCQ,EAAEsB,MAAMqB,QAAQ,QAAQC,YAExBJ,GAAG,QAASnD,UAAUC,gBAAkB,uBAAuB,SAAUmD,GACzEA,EAAEI,qBAEEhD,IAAMyC,kBAEVtC,EAAEX,UAAUG,eAAesD,IAAI,IAC/B9C,EAAEX,UAAUG,eAAeuD,OAAO,YAAYC,KAAK,WAAW,GAC9DhD,EAAEX,UAAUG,eAAeuD,OAAO,gBAAgBC,KAAK,WAAW,GAIlEpD,eAAeC,QAEf2C,GAAG,SAAUnD,UAAUC,gBAAkB,kBAAkB,SAAUmD,GACrEA,EAAEI,iBAKFjD,eAHU0C,kBAGU,CAAE/C,QAFR0D,wBAIdT,GAAG,QAASnD,UAAUK,WAAa,eAAe,SAAU+C,GAC5DA,EAAEI,qBAEEhD,IAAMyC,kBACN/C,QAAU0D,mBACIZ,mBAIlBzC,eAAeC,IAAK,CAAEqD,KAHNlD,EAAEsB,MAAMP,KAAK,QAGKxB,QAASA,aAE3CiD,GAAG,QAAS,0BAA0B,SAAUC,GAChDA,EAAEI,qBAEEM,QAAUnD,EAAEsB,MACZ8B,SAAWD,QAAQpC,KAAK,YACxBsC,OAAsC,GAA7BF,QAAQpC,KAAK,cAAkD,GAA7BoC,QAAQpC,KAAK,0BAExDuC,cAAc,CACjBC,QAAS,CACR,CACCC,UAAW,KACXC,GAAIL,SACJM,UAAWL,WAIZ1C,MAAMgD,SACFA,OAAOC,UACVD,OAAOC,SAASC,SAASC,UACxB9B,aAAa+B,gBAAgB,CAC5BC,QAASF,QAAQE,QACjBC,KAAM,WAGA,SAILpE,IAAMyC,kBACN/C,QAAU0D,mBACVC,KAAOb,mBACP6B,OAASb,OAAS,EAAI,EAE1BF,QAAQpC,KAAK,YAAamD,QAC1Bf,QAAQgB,KAAK,KAAKC,YAAY,cAAeF,QAC7Cf,QAAQgB,KAAK,KAAKC,YAAY,KAAMF,QAEpCtE,eAAeC,IAAK,CAAEqD,KAAMA,KAAM3D,QAASA,SAA3C,OAIJS,EAAEqE,QAAQ7B,GAAG,SAAUX,2BAOlBX,wBAA2BoD,aAE7BzD,QAAQC,IAAI,qCACZD,QAAQC,IAAIwD,SAEPA,gBAAAA,QAASC,aAAsC,GAAvBD,QAAQC,kBAC1B,OAGPC,aAAeH,OAAOI,YAAc,IACpC/E,WAAa,CACbgF,MAAO,OACPC,MAAO,GACPC,UAAU,EACVC,SAAUP,QAAQQ,UAAY,IAG9BC,YAAcT,QAAQpB,KACtB8B,WAAaV,QAAQC,YAErBQ,YAAc,IACdrF,WAAWuF,SAAW,CAAE/B,KAAM6B,YAAc,EAAGG,oBAAcH,YAAc,KAG3EA,YAAcC,aACdtF,WAAWyF,KAAO,CAAEjC,KAAM6B,YAAc,EAAGG,oBAAcH,YAAc,SAGvEK,SAAW,KACXJ,YAAc,MACT,IAAIK,EAAI,EAAGA,GAAKL,WAAYK,IAC7B3F,WAAWiF,MAAMW,KAAK,CAAEpC,KAAMmC,EAAGE,OAAQF,IAAMN,YAAaG,oBAAcG,aAKzEN,aAAe,IAAMP,cAAkBO,aAAe,GAAKP,aAAe,IAEvEO,aAAeC,WAAa,IAC5BI,SAAW,GAGVZ,iBAMI,IAAIa,EAAI,EAAGA,GAAK,EAAGA,IACpB3F,WAAWiF,MAAMW,KAAK,CAAEpC,KAAMmC,EAAGE,OAAQF,IAAMN,YAAaG,oBAAcG,cANzE,IAAIA,EAAI,EAAGA,GAAKN,YAAc,GAAKM,GAAKL,WAAaI,SAAUC,IAChE3F,WAAWiF,MAAMW,KAAK,CAAEpC,KAAMmC,EAAGE,OAAQF,IAAMN,YAAaG,oBAAcG,KASlE,GAAZD,UAAiBZ,aACjB9E,WAAW8F,KAAO,CAAEtC,KAAM8B,WAAYO,OAAQP,YAAcD,YAAaG,oBAAcF,aAElFD,YAAc,EAAIC,YACvBtF,WAAWiF,MAAMW,KAAK,CAAEpC,KAAM8B,WAAYO,OAAQP,aAAeD,YAAaG,oBAAcF,mBAG7F,GAAKD,YAAcC,WAAa,IAAMR,cAAkBO,YAAcC,WAAa,GAAKR,aAAe,CAE1GY,SAAW,EACPZ,aACIO,YAAc,IACdK,SAAWJ,WAAa,EACxBtF,WAAW+F,MAAQ,CAAEvC,KAAM,EAAGqC,OAAQ,GAAKR,YAAaG,oBAAc,KAGrEH,aAAe,IACpBK,SAAWL,YAAc,EACzBrF,WAAW+F,MAAQ,CAAEvC,KAAM,EAAGqC,OAAQ,GAAKR,YAAaG,oBAAc,SAGrE,IAAIG,EAAID,SAAUC,GAAKL,WAAYK,IACpC3F,WAAWiF,MAAMW,KAAK,CAAEpC,KAAMmC,EAAGE,OAAQF,IAAMN,YAAaG,oBAAcG,SAE3E,IAEH3F,WAAW+F,MAAQ,CAAEvC,KAAM,EAAGqC,OAAQ,GAAKR,YAAaG,oBAAc,IAClEV,aACA9E,WAAWiF,MAAMW,KAAK,CAAEpC,KAAM6B,YAAaQ,QAAQ,EAAML,oBAAcH,wBAGlE,IAAIM,EAAIN,YAAc,EAAGM,GAAKN,YAAc,EAAGM,IAChD3F,WAAWiF,MAAMW,KAAK,CAAEpC,KAAMmC,EAAGE,OAAQF,IAAMN,YAAaG,oBAAcG,KAGlF3F,WAAW8F,KAAO,CAAEtC,KAAM8B,WAAYO,OAAQP,YAAcD,YAAaG,oBAAcF,oBAGxFtF,UAAP,WAGKuD,uBACJyC,YAAc1F,EAAEX,UAAUC,gBAAkB,kBAAkBqG,iBAC9DpG,QAAU,UAEdS,EAAEmB,KAAKuE,aAAa,WACfpE,KAAKsE,QACRrG,QAAQ+B,KAAKuE,MAAQvE,KAAKsE,UAIrBrG,iBAGC+C,sBACJwD,WAAa9F,EAAEX,UAAUC,gBAAkB,2BAC/CuB,QAAQC,IAAI,gBACZD,QAAQC,IAAIgF,YACLA,WAAW/E,KAAK,UAAU2B,QAAQ,IAAK,aAGtCb,2BAGFkE,aAAe1G,UAAUC,gBAAkB,SAC3C0G,UAAYzD,SAAS0D,cAAc5G,UAAUC,iBAE7CkC,MAAQe,SAAS2D,iBAAiBH,kBACnCvE,MAAM2E,OAAQ,OAAO,QAEpBC,WAAaC,MAAMC,KAAK9E,OAAO+E,KAAKC,MAASA,KAAKC,cAGlDC,iBAVoB,IAQPC,KAAKC,OAAOR,YATN,IAYnBS,gBAAkBH,iBAAmB,GAE3CV,UAAUc,MAAMC,YAAY,yBAAmBL,wBAC/CV,UAAUc,MAAMC,YAAY,0BAAoBF,6BAE1CG,iBAAmBjB,aAAe,cAClCkB,WAAa1E,SAAS2D,iBAAiBc,kBAEvCE,gBAAkBb,MAAMC,KAAKW,YAAYV,KAC7CY,MAASA,KAAKC,eAIVC,sBAFmBV,KAAKC,OAAOM,iBAEYR,iBAAmB,SACpEV,UAAUc,MAAMC,YAAY,gCAA0BM,8BAE/C"}