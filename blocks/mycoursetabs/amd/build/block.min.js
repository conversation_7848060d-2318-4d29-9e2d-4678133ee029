define("block_mycoursetabs/block",["exports","core/ajax","core/templates","core/notification","local_courseblockapi/api"],(function(_exports,_ajax,_templates,Notification,_api){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_ajax=_interopRequireDefault(_ajax),_templates=_interopRequireDefault(_templates),Notification=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Notification),_api=_interopRequireDefault(_api);const SELECTORS={block_container:".mycoursetabs",filters:".mycoursetabs .filters",filters_input:".mycoursetabs .filters input, .mycoursetabs .filters select",filters_recommended:".mycoursetabs .filters input[name='recommended']",pagination:".mycoursetabs .pagination-wrapper",footer_container:".mycoursetabs .courses-page-footer"};async function render_courses(tab){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{filters:{}},$loading=$(SELECTORS.block_container+" .smart-loading"),$currentTabCards=$(SELECTORS.block_container+" .tab-pane#"+tab+" .cards"),$otherTabsCards=$(SELECTORS.block_container+" .tab-pane:not(#"+tab+") .cards");$loading.addClass("show"),async function(){const request={methodname:{mycourses:"block_mycoursetabs_get_my_courses",allcourses:"block_mycoursetabs_get_all_courses"}[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mycourses"],args:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}};return _ajax.default.call([request])[0]}(tab,args).then((async data=>{console.log(data),console.log(data.query),console.log(data.filters);let paginationData=prepare_page_pagination(data);console.log("paginationData: "),console.log(paginationData),$.each(data.items,(function(){this.display_coursename=this.display_coursename&&["Sim","Yes"].includes(this.display_coursename)||!1}));let cards=await _templates.default.render("block_mycoursetabs/block-cards",data),pagination=paginationData?await _templates.default.render("block_mycoursetabs/pagination",paginationData):"";$currentTabCards.html(cards),$(SELECTORS.pagination).html(pagination),$loading.removeClass("show"),$otherTabsCards.html(""),reset_cards_height()})).catch((error=>{console.error(error),Notification.exception()}))}async function render_filters(tab){(async function(){const request={methodname:"block_mycoursetabs_get_filters",args:{tab:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mycourses",filtered:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}}};return _ajax.default.call([request])[0]})(tab,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).then((async data=>{let filters=await _templates.default.render("block_mycoursetabs/block-filters",{filters:data});$(SELECTORS.filters).html(filters)}))}function get_current_page(){return $(SELECTORS.pagination+" .page-item.active button.page-link").data("page")||1}_exports.init=async function(){console.log("Initializing My Course Tabs block...");let tab=get_current_tab();console.log(tab),render_courses(tab),render_filters(tab),$(document).on("click",SELECTORS.block_container+" .tablink",(function(e){let tab=$(this).data("target").replace("#","");$(SELECTORS.pagination).html(""),render_courses(tab),render_filters(tab)})).on("change",SELECTORS.filters_input,(function(){$(this).closest("form").submit()})).on("click",SELECTORS.block_container+" .btn-clear-filters",(function(e){e.preventDefault();let tab=get_current_tab();$(SELECTORS.filters_input).val(""),$(SELECTORS.filters_input).filter(":checked").prop("checked",!1),$(SELECTORS.filters_input).filter("#recommended").prop("checked",!0),render_courses(tab)})).on("submit",SELECTORS.block_container+" .filters form",(function(e){e.preventDefault(),render_courses(get_current_tab(),{filters:get_filters_data()})})).on("click",SELECTORS.pagination+" .page-link",(function(e){e.preventDefault();let tab=get_current_tab(),filters=get_filters_data();get_current_page();render_courses(tab,{page:$(this).data("page"),filters:filters})})).on("click",".card-course-favourite",(function(e){e.preventDefault();let $button=$(this),courseid=$button.data("courseid"),status=0==$button.data("favourite")||0==$button.data("favourite");_api.default.set_favourite({courses:[{component:null,id:courseid,favourite:status}]}).then((result=>{result.warnings&&result.warnings.forEach((warning=>(Notification.addNotification({message:warning.message,type:"error"}),!1)));let tab=get_current_tab(),filters=get_filters_data(),page=get_current_page(),toggle=status?1:0;$button.data("favourite",toggle),$button.find("i").toggleClass("fa-regular",!toggle),$button.find("i").toggleClass("fa",toggle),render_courses(tab,{page:page,filters:filters})}))})),$(window).on("resize",reset_cards_height)};const prepare_page_pagination=results=>{if(console.log("prepare_page_pagination results: "),console.log(results),null==results||!results.total_pages||1==results.total_pages)return{};let isMobileView=window.innerWidth<=600,pagination={label:"Page",pages:[],haspages:!0,pagesize:results.per_page||10},currentPage=results.page,totalPages=results.total_pages;currentPage>1&&(pagination.previous={page:currentPage-1,url:"?page=".concat(currentPage-1)}),currentPage<totalPages&&(pagination.next={page:currentPage+1,url:"?page=".concat(currentPage+1)});let treshold=0;if(totalPages<=5)for(let i=1;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});else if(currentPage<=5&&!isMobileView||currentPage<=3&&isMobileView){if(currentPage<=totalPages-5&&(treshold=2),isMobileView)for(let i=1;i<=3;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});else for(let i=1;i<=currentPage+3&&i<=totalPages-treshold;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});2==treshold||isMobileView?pagination.last={page:totalPages,active:totalPages==currentPage,url:"?page=".concat(totalPages)}:currentPage+3<totalPages&&pagination.pages.push({page:totalPages,active:totalPages===currentPage,url:"?page=".concat(totalPages)})}else if(currentPage>totalPages-5&&!isMobileView||currentPage>totalPages-3&&isMobileView){treshold=1,isMobileView?currentPage>3&&(treshold=totalPages-2,pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)}):currentPage>=6&&(treshold=currentPage-3,pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)});for(let i=treshold;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)})}else{if(pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)},isMobileView)pagination.pages.push({page:currentPage,active:!0,url:"?page=".concat(currentPage)});else for(let i=currentPage-3;i<=currentPage+3;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});pagination.last={page:totalPages,active:totalPages==currentPage,url:"?page=".concat(totalPages)}}return pagination};function get_filters_data(){let filtersData=$(SELECTORS.block_container+" .filters form").serializeArray(),filters={};return $.each(filtersData,(function(){this.value&&(filters[this.name]=this.value)})),filters}function get_current_tab(){let $activeTab=$(SELECTORS.block_container+" .tablink.active");return console.log("active Tab: "),console.log($activeTab),$activeTab.data("target").replace("#","")}function reset_cards_height(){const cardSelector=SELECTORS.block_container+" .card",container=document.querySelector(SELECTORS.block_container),cards=document.querySelectorAll(cardSelector);if(!cards.length)return!1;const cardWidths=Array.from(cards).map((card=>card.scrollWidth)),new_image_height=350*Math.max(...cardWidths)/600,new_card_height=new_image_height+16;container.style.setProperty("--img-height","".concat(new_image_height,"px")),container.style.setProperty("--card-height","".concat(new_card_height,"px"));const cardBodySelector=cardSelector+" .card-body",cardBodies=document.querySelectorAll(cardBodySelector),cardBodyHeights=Array.from(cardBodies).map((body=>body.scrollHeight)),new_card_height_hover=Math.max(...cardBodyHeights)+new_image_height+5;return container.style.setProperty("--card-height-hover","".concat(new_card_height_hover,"px")),!0}}));

//# sourceMappingURL=block.min.js.map