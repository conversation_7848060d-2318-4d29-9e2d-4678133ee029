import Ajax from 'core/ajax';
import Templates from 'core/templates';
import * as Notification from "core/notification";
import API from "local_courseblockapi/api";

const SELECTORS = {
	block_container: ".mycoursetabs",
	filters: ".mycoursetabs .filters",
	filters_input: ".mycoursetabs .filters input, .mycoursetabs .filters select",
	pagination: ".mycoursetabs .pagination-wrapper",
	footer_container: ".mycoursetabs .courses-page-footer",
}

async function get_courses(tab = "mycourses", args = {}) {
	let method = {
		mycourses: "block_mycoursetabs_get_my_courses",
		allcourses: "block_mycoursetabs_get_all_courses",
	}

	const request = {
		methodname: method[tab],
		args: args,
	};

	return Ajax.call([request])[0];
}

async function get_tab_filters(tab = "mycourses", filtered = {}) {
	const request = {
		methodname: "block_mycoursetabs_get_filters",
		args: {
			tab: tab,
			filtered: filtered,
		},
	};

	return Ajax.call([request])[0];
}

async function render_courses(tab, args = { filters: {} }) {
	let $loading = $(SELECTORS.block_container + " .smart-loading");
	let $currentTabCards = $(SELECTORS.block_container + " .tab-pane#" + tab + " .cards");
	let $otherTabsCards = $(SELECTORS.block_container + " .tab-pane:not(#" + tab + ") .cards");

	$loading.addClass("show");

	get_courses(tab, args)
		.then(async (data) => {
			console.log(data);
			console.log(data.query);
			console.log(data.filters);
			let paginationData = prepare_page_pagination(data);
            console.log("paginationData: ");
            console.log(paginationData);
			// Tratar isso no backend futuramente
			$.each(data.items, function () {
				this.display_coursename = this.display_coursename && ["Sim", "Yes"].includes(this.display_coursename) || false;
			})

			let cards = await Templates.render("block_mycoursetabs/block-cards", data);
			let pagination = paginationData ? await Templates.render("block_mycoursetabs/pagination", paginationData) : "";

			$currentTabCards.html(cards);
			$(SELECTORS.pagination).html(pagination);

			$loading.removeClass("show");
			$otherTabsCards.html("");
			reset_cards_height();
		})
		.catch(error => {
			console.error(error);
			Notification.exception();
		})
}

async function render_filters(tab, filtered = {}) {
	get_tab_filters(tab, filtered)
		.then(async (data) => {
			let filters = await Templates.render("block_mycoursetabs/block-filters", { filters: data });
			$(SELECTORS.filters).html(filters);
		});
}

export const init = async function () {
	// Carrega a aba selecionada padrão
	console.log("Initializing My Course Tabs block...");
	let tab = get_current_tab();
	console.log(tab);
	render_courses(tab);
	render_filters(tab);

	$(document).on("click", SELECTORS.block_container + " .tablink", function (e) {
		let tab = $(this).data("target").replace("#", "");
		$(SELECTORS.pagination).html("");

		render_courses(tab);
		render_filters(tab);
	})
		.on("change", SELECTORS.filters_input, function () {
			$(this).closest("form").submit();
		})
		.on("click", SELECTORS.block_container + " .btn-clear-filters", function (e) {
			e.preventDefault();

			let tab = get_current_tab();

			$(SELECTORS.filters_input).val("");
			$(SELECTORS.filters_input).filter(":checked").prop("checked", false);
			$(SELECTORS.filters_input).filter("#recommended").prop("checked", true);

			render_courses(tab);
		})
		.on("submit", SELECTORS.block_container + " .filters form", function (e) {
			e.preventDefault();

			let tab = get_current_tab();
			let filters = get_filters_data();

			render_courses(tab, { filters: filters });
		})
		.on("click", SELECTORS.pagination + " .page-link", function (e) {
			e.preventDefault();

			let tab = get_current_tab();
			let filters = get_filters_data();
			let currentPage = get_current_page();
			let pageValue = $(this).data("page");
			let page = pageValue;

			render_courses(tab, { page: page, filters: filters });
		})
		.on("click", ".card-course-favourite", function (e) {
			e.preventDefault();

			let $button = $(this);
			let courseid = $button.data("courseid");
			let status = $button.data("favourite") == 0 || $button.data("favourite") == false;

			API.set_favourite({
				courses: [
					{
						component: null, //null means it favourites in system context
						id: courseid,
						favourite: status,
					},
				],
			})
				.then((result) => {
					if (result.warnings) {
						result.warnings.forEach((warning) => {
							Notification.addNotification({
								message: warning.message,
								type: "error",
							});

							return false;
						});
					}

					let tab = get_current_tab();
					let filters = get_filters_data();
					let page = get_current_page();
					let toggle = status ? 1 : 0;

					$button.data("favourite", toggle);
					$button.find("i").toggleClass("fa-regular", !toggle);
					$button.find("i").toggleClass("fa", toggle);

					render_courses(tab, { page: page, filters: filters });
				})
		})

	$(window).on("resize", reset_cards_height);
}

function get_current_page() {
    return $(SELECTORS.pagination + " .page-item.active button.page-link").data("page") || 1;
}

const prepare_page_pagination = (results) => {
    
    console.log("prepare_page_pagination results: ");
    console.log(results);

    if (!results?.total_pages || results.total_pages == 1) {
        return {};
    }

    let isMobileView = window.innerWidth <= 600/*  */
    let pagination = {
        label: "Page",
        pages: [],
        haspages: true,
        pagesize: results.per_page || 10,
    };

    let currentPage = results.page;
    let totalPages = results.total_pages;

    if (currentPage > 1) {
        pagination.previous = { page: currentPage - 1, url: `?page=${currentPage - 1}` };
    }

    if (currentPage < totalPages) {
        pagination.next = { page: currentPage + 1, url: `?page=${currentPage + 1}` };
    }

    let treshold = 0;
    if (totalPages <= 5) {
        for (let i = 1; i <= totalPages; i++) {
            pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
        }
    }
    else {

        if ((currentPage <= 5 && !isMobileView) || (currentPage <= 3 && isMobileView)) {
            // primeiras páginas
            if (currentPage <= totalPages - 5) {
                treshold = 2;
            }

            if (!isMobileView) {
                for (let i = 1; i <= currentPage + 3 && i <= totalPages - treshold; i++) {
                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
                }
            }
            else {
                for (let i = 1; i <= 3; i++) {
                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
                }
            }

            if (treshold == 2 || isMobileView) {
                pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };
            }
            else if (currentPage + 3 < totalPages) {
                pagination.pages.push({ page: totalPages, active: totalPages === currentPage, url: `?page=${totalPages}` });
            }

        } else if ((currentPage > totalPages - 5 && !isMobileView) || (currentPage > totalPages - 3 && isMobileView)) {
            // últimas páginas
            treshold = 1;
            if (isMobileView) {
                if (currentPage > 3) {
                    treshold = totalPages - 2;
                    pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
                }
            }
            else if (currentPage >= 6) {
                treshold = currentPage - 3;
                pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
            }

            for (let i = treshold; i <= totalPages; i++) {
                pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
            }
        } else {
            // meio
            pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
            if (isMobileView) {
                pagination.pages.push({ page: currentPage, active: true, url: `?page=${currentPage}` });
            }
            else {
                for (let i = currentPage - 3; i <= currentPage + 3; i++) {
                    pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
                }
            }
            pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };
        }
    }
    return pagination;
};

function get_filters_data() {
	let filtersData = $(SELECTORS.block_container + " .filters form").serializeArray();
	let filters = {};

	$.each(filtersData, function () {
		if (this.value) {
			filters[this.name] = this.value;
		}
	});

	return filters;
};

function get_current_tab() {
	let $activeTab = $(SELECTORS.block_container + " .tablink.active");
	console.log("active Tab: ");
	console.log($activeTab);
	return $activeTab.data("target").replace("#", "");
}

function reset_cards_height() {
	const imgOriginalWidth = 600;
	const imgOriginalHeight = 350;
	const cardSelector = SELECTORS.block_container + " .card";
	const container = document.querySelector(SELECTORS.block_container);

	const cards = document.querySelectorAll(cardSelector);
	if (!cards.length) return false;

	const cardWidths = Array.from(cards).map((card) => card.scrollWidth);
	const card_width = Math.max(...cardWidths);

	const new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;
	const new_card_height = new_image_height + 16; // add 16px

	container.style.setProperty("--img-height", `${new_image_height}px`);
	container.style.setProperty("--card-height", `${new_card_height}px`);

	const cardBodySelector = cardSelector + " .card-body";
	const cardBodies = document.querySelectorAll(cardBodySelector);

	const cardBodyHeights = Array.from(cardBodies).map(
		(body) => body.scrollHeight
	);
	const card_body_height = Math.max(...cardBodyHeights);

	const new_card_height_hover = card_body_height + new_image_height + 5;
	container.style.setProperty("--card-height-hover", `${new_card_height_hover}px`);

	return true;
};
