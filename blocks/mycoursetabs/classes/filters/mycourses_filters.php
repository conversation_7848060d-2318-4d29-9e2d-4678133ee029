<?php namespace block_mycoursetabs\filters;

use \tool_usercoursestatus\utils\constants AS status_constants;
use \block_mycoursetabs\filters\abstract_filters;
use \local_offermanager\constants as offermanager_constants;

require_once($CFG->dirroot."/local/courseblockapi/lib.php");

class mycourses_filters extends abstract_filters {

    public static function get_tab_name() : string {
        return 'mycourses';
    }

    protected function make_status_filter(): object {
        $selected = $this->get_current_value('status', -1);

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];
        
        foreach (offermanager_constants::get_situation_list() as $value => $name) {
            $filter->options[] = (object)[
                "name"     => $name,
                "value"    => $value,
                "selected" => $value == $selected,
            ];
        }

        return $filter;
    }

    protected function make_level_filter(): object
    {
        $filter = (object) [
            'enabled' => false,
            'options' => [],
        ];

        return $filter;
    }

    protected function make_status_progress_filter() : object {

        $selected = $this->get_current_value('status_progress', -1);

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];

        $statusoptions = [
            0 => get_string('not_started', 'local_courseblockapi'),
            1 => get_string('in_progress', 'local_courseblockapi'),
            2 => get_string('completed', 'local_courseblockapi'),
        ];

        foreach ($statusoptions as $value => $name) {
            $filter->options[] = (object)[
                "name"     => $name,
                "value"    => $value,
                "selected" => $value == $selected,
            ];
        }

        return $filter;
    }

    protected function make_most_accessed_filter(): object
    {
        $filter = (object) [
            'enabled' => false,
            'options' => [],
        ];

        return $filter;
    }

    protected function make_recommended_filter() : object {

        $filter = (object)[
            'enabled' => true,
            'value' => 1,
            'checked' => true,
        ];

        return $filter;
    }

}