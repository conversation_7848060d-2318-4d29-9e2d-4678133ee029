<?php namespace block_mycoursetabs\external;

use \StdClass;
use \context_system;
use \core_external\external_api;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \core_external\external_single_structure;
use \core_external\external_value;
use \invalid_parameter_exception;
use \block_mycoursetabs\external\abstract_paginated_api;
use \block_mycoursetabs\external\exporter\course_card_exporter;
use tool_lfxp\util\query_object;
use \tool_usercoursestatus\utils\constants as status_constants;
use \local_offermanager\constants as offermanager_constants;
class my_courses_api extends abstract_paginated_api {

    protected static function define_filters_structure() : external_single_structure {
        return new external_single_structure([
            'categoryid'      => new external_value(PARAM_INT, 'Category ID', VALUE_DEFAULT, 0),
            'favourite'       => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            'recommended'     => new external_value(PARAM_BOOL, 'Only recommended course', VALUE_DEFAULT, true),
            'uf'              => new external_value(PARAM_TEXT, 'UF', VALUE_DEFAULT, 'all'),
            'modality'        => new external_value(PARAM_TEXT, 'Course modality filter', VALUE_DEFAULT, 'all'),
            'search'          => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            'status'          => new external_value(PARAM_INT, 'Enrolment status', VALUE_DEFAULT, -1),
            'status_progress' => new external_value(PARAM_INT, 'progress status', VALUE_DEFAULT, -1),
        ], VALUE_DEFAULT, []);
    }
    
    public static function get_my_courses_parameters(){
        return self::append_common_pagination_parameters([
            'filters' => self::define_filters_structure(),
        ]);
    }


    public static function get_my_courses(int $page = 1, int $per_page = 16, array $filters = [])
    {
        global $OUTPUT, $USER, $PAGE;

        if ($page < 1) {
            $page = 1;
        }

        $courses = [];
        $params = compact('page', 'per_page', 'filters');
        $params = self::validate_parameters(self::get_my_courses_parameters(), $params);

        $context = context_system::instance();
        require_capability('moodle/category:viewcourselist', $context);
        $PAGE->set_context($context);

        $renderer = $PAGE->get_renderer('block_mycoursetabs');

        $query = self::build_query($params['filters']);
        $recordset = $query->get_recordset(($page - 1) * $per_page, $per_page);

        foreach ($recordset as $record) {
            $record->completed = in_array($record->statusid, [
                offermanager_constants::OFFER_USER_ENROL_SITUATION_APPROVED,
                offermanager_constants::OFFER_USER_ENROL_SITUATION_COMPLETED,
            ]);

            $record->is_enrolled = in_array($record->statusid, [
                offermanager_constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
                offermanager_constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
                offermanager_constants::OFFER_USER_ENROL_SITUATION_ABANDONED,
            ]);;

            $exporter = new course_card_exporter($record, ['userid' => (int) $USER->id]);
            $courses[] = $exporter->export($renderer);
        }

        $recordset->close();
        $total = $query->count();
        
        return [
            'page' => $page,
            'per_page' => $per_page,
            'total' => $total,
            'total_pages' => ceil($total / $per_page),
            'show_all' => $total > COUNT($courses),
            'items' => $courses,
            'empty_url' => !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '',
            'filters' => $params['filters'],
            'query' => json_encode($query),
        ];
    }


    public static function get_my_courses_returns()
    {
        return self::append_common_pagination_returns([
            'items' => new external_multiple_structure(
                course_card_exporter::get_read_structure(VALUE_OPTIONAL),
                'List of courses',
                VALUE_DEFAULT,
                []
            ),
            'filters' => self::define_filters_structure(),
            'query' => new external_value(PARAM_RAW, 'Debug query info', VALUE_OPTIONAL),
        ]);
    }


    protected static function build_query($filters): query_object {
        global $USER, $DB;

        $user_audience_ids = $DB->get_fieldset_sql(
            'SELECT audienceid FROM {local_audience_members} WHERE userid = ?',
            [$USER->id]
        );

        if (empty($user_audience_ids)) {
            $user_audience_ids = [0];
        }

        $query = new query_object();
        list($in_sql, $in_params) = $DB->get_in_or_equal($user_audience_ids, SQL_PARAMS_NAMED, 'audienceid');

        $query->fields = "
            DISTINCT mc.id,
            lou.userid,
            mc.fullname,
            mcc.id AS categoryid,
            mcc.name AS category,
            mf.id IS NOT NULL AS is_favorite,
            lou.situation,
            lou.progress";

        $query->from = "
            {local_offermanager_ue} lou
            JOIN {course} mc ON (mc.id = lou.courseid)
            JOIN {course_categories} mcc ON (mcc.id = mc.category AND mcc.visible = 1)
            JOIN {local_offermanager_class} l_o_cl ON (l_o_cl.id = lou.offerclassid)
            JOIN {local_offermanager_course} l_o_c ON (l_o_c.id = l_o_cl.offercourseid)
            JOIN {local_offermanager_audience} l_o_a ON (l_o_a.offerid = l_o_c.offerid)
            LEFT JOIN {local_custom_fields_course} mlcfc ON (mlcfc.courseid = mc.id)
            LEFT JOIN {favourite} mf ON (mf.itemid = mc.id AND mf.userid = :userid_fav AND mf.itemtype = 'courses')";

        $query->add_where_condition("lou.userid = :userid");
        $query->add_where_condition("mc.visible = 1");

        //filtro "Indicados para mim"
        if (!empty($filters['recommended'])) {
            $query->add_where_condition("l_o_a.audienceid " . $in_sql);
        } else {
            $query->add_where_condition("l_o_a.audienceid NOT " . $in_sql);
        }
        
        // Parâmetros
        $params = array_merge($in_params, [
            'userid' => $USER->id,
            'userid_fav' => $USER->id,
        ]);

        // Ordenação padrão
        $query->order = 'mc.fullname ASC';

        // --- FILTROS ---
        if (!empty($filters['favourite'])) {
            $query->add_where_condition('mf.id IS NOT NULL');
        }

        if (isset($filters['status']) && $filters['status'] != -1) {
            $query->add_where_condition('lou.situation = :situation');
            $params['situation'] = $filters['status'];
        }

        if (isset($filters['status_progress']) && $filters['status_progress'] != -1) {
            switch ($filters['status_progress']) {
                case 0: $query->add_where_condition('lou.progress = 0'); break;
                case 1: $query->add_where_condition('lou.progress > 0 AND lou.progress < 100'); break;
                case 2: $query->add_where_condition('lou.progress = 100'); break;
            }
        }

        if (!empty($filters['categoryid'])) {
            $catid = $filters['categoryid'];
            $like_path_sql = $DB->sql_like('mcc.path', ':catpath');
            $eq_id_sql = 'mcc.id = :catid';
            $query->add_where_condition("($like_path_sql OR $eq_id_sql)");
            $params['catpath'] = '%/' . $catid . '/%';
            $params['catid'] = $catid;
        }

        if (!empty($filters['uf']) && $filters['uf'] !== 'all') {
            $ufcondition = $DB->sql_like('mlcfc.course_uf', ':uf', false);
            $query->add_where_condition($ufcondition);
            $params['uf'] = '%' . trim($filters['uf']) . '%';
        }

        if (!empty($filters['modality']) && $filters['modality'] !== 'all') {
            $query->add_where_condition($DB->sql_like('mlcfc.modality', ':modality'));
            $params['modality'] = '%' . trim($filters['modality']) . '%';
        }

        if (!empty($filters['search'])) {
            $searchcondition = $DB->sql_like('mc.fullname', ':searchtext', false);
            $query->add_where_condition($searchcondition);
            $params['searchtext'] = '%' . trim($filters['search']) . '%';
        }

        $query->params = $params;
        
        return $query;
    }
}