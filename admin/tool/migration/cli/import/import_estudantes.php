<?php

use tool_migration\importers\estudante_importer;

define("CLI_SCRIPT", true);
require_once(__DIR__ . '/../../../../../config.php');

raise_memory_limit(MEMORY_HUGE);



function index_existing_users() {
    global $DB;

    mtrace("Indexando usuários por cpf...");

    // Usa recordset para baixo consumo de memória
    $sql = "
        SELECT ee.id AS eid, u.id AS uid
        FROM {eadtech_estudantes} ee
        JOIN {user} u ON u.username = ee.source_cpf
        WHERE ee.instanceid = 0
    ";
    $rs = $DB->get_recordset_sql($sql);

    $updated = 0;

    foreach ($rs as $record) {
        $estudante = new stdClass();
        $estudante->id = $record->eid;
        $estudante->instanceid = $record->uid;
        $estudante->needs_update = 1;

        $DB->update_record('eadtech_estudantes', $estudante);
        $updated++;

        if ($updated % 100 === 0) {
            mtrace("Indexados até agora: {$updated}");
        }
    }

    $rs->close();

    mtrace("Total de indexados: {$updated}\n");
}


index_existing_users();

$importer = new estudante_importer(new text_progress_trace());
$importer->import_pending();

$importer = new estudante_importer(new text_progress_trace());
$importer->update_pending();