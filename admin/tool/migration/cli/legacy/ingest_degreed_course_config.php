<?php

require_once(__DIR__ . '/config.php');

use local_degreed_integration\models\course_sync_record;
use \local_legacy\models\curso;

require_once("$CFG->dirroot/course/lib.php");

function custom_update_course($data, $editoroptions = NULL) {
    global $DB, $CFG;

    // Prevent changes on front page course.
    if ($data->id == SITEID) {
        throw new moodle_exception('invalidcourse', 'error');
    }

    $oldcourse = course_get_format($data->id)->get_course();
    $context   = context_course::instance($oldcourse->id);

    // Make sure we're not changing whatever the course's relativedatesmode setting is.
    unset($data->relativedatesmode);

    // Capture the updated fields for the log data.
    $updatedfields = [];
    foreach (get_object_vars($oldcourse) as $field => $value) {
        if ($field == 'summary_editor') {
            if (($data->$field)['text'] !== $value['text']) {
                // The summary might be very long, we don't wan't to fill up the log record with the full text.
                $updatedfields[$field] = '(updated)';
            }
        } else if ($field == 'tags' && isset($data->tags)) {
            // Tags might not have the same array keys, just check the values.
            if (array_values($data->$field) !== array_values($value)) {
                $updatedfields[$field] = $data->$field;
            }
        } else {
            if (isset($data->$field) && $data->$field != $value) {
                $updatedfields[$field] = $data->$field;
            }
        }
    }

    $data->timemodified = time();

    if ($editoroptions) {
        $data = file_postupdate_standard_editor($data, 'summary', $editoroptions, $context, 'course', 'summary', 0);
    }
    if ($overviewfilesoptions = course_overviewfiles_options($data->id)) {
        $data = file_postupdate_standard_filemanager($data, 'overviewfiles', $overviewfilesoptions, $context, 'course', 'overviewfiles', 0);
    }

    // Check we don't have a duplicate shortname.
    if (!empty($data->shortname) && $oldcourse->shortname != $data->shortname) {
        if ($DB->record_exists_sql('SELECT id from {course} WHERE shortname = ? AND id <> ?', array($data->shortname, $data->id))) {
            throw new moodle_exception('shortnametaken', '', '', $data->shortname);
        }
    }

    // Check we don't have a duplicate idnumber.
    if (!empty($data->idnumber) && $oldcourse->idnumber != $data->idnumber) {
        if ($DB->record_exists_sql('SELECT id from {course} WHERE idnumber = ? AND id <> ?', array($data->idnumber, $data->id))) {
            throw new moodle_exception('courseidnumbertaken', '', '', $data->idnumber);
        }
    }

    if ($errorcode = course_validate_dates((array)$data)) {
        throw new moodle_exception($errorcode);
    }

    if (!isset($data->category) or empty($data->category)) {
        // prevent nulls and 0 in category field
        unset($data->category);
    }
    $changesincoursecat = $movecat = (isset($data->category) and $oldcourse->category != $data->category);

    if (!isset($data->visible)) {
        // data not from form, add missing visibility info
        $data->visible = $oldcourse->visible;
    }

    if ($data->visible != $oldcourse->visible) {
        // reset the visibleold flag when manually hiding/unhiding course
        $data->visibleold = $data->visible;
        $changesincoursecat = true;
    } else {
        if ($movecat) {
            $newcategory = $DB->get_record('course_categories', array('id'=>$data->category));
            if (empty($newcategory->visible)) {
                // make sure when moving into hidden category the course is hidden automatically
                $data->visible = 0;
            }
        }
    }

    // Set newsitems to 0 if format does not support announcements.
    if (isset($data->format)) {
        $newcourseformat = course_get_format((object)['format' => $data->format]);
        if (!$newcourseformat->supports_news()) {
            $data->newsitems = 0;
        }
    }

    // Set showcompletionconditions to null when completion tracking has been disabled for the course.
    if (isset($data->enablecompletion) && $data->enablecompletion == COMPLETION_DISABLED) {
        $data->showcompletionconditions = null;
    }

    // Update custom fields if there are any of them in the form.
    $handler = core_course\customfield\course_handler::create();
    $handler->instance_form_save($data);

    // Update with the new data
    $DB->update_record('course', $data);
    // make sure the modinfo cache is reset
    rebuild_course_cache($data->id);

    // Purge course image cache in case if course image has been updated.
    \cache::make('core', 'course_image')->delete($data->id);

    // update course format options with full course data
    course_get_format($data->id)->update_course_format_options($data, $oldcourse);

    $course = $DB->get_record('course', array('id'=>$data->id));

    if ($movecat) {
        $newparent = context_coursecat::instance($course->category);
        $context->update_moved($newparent);
    }
    $fixcoursesortorder = $movecat || (isset($data->sortorder) && ($oldcourse->sortorder != $data->sortorder));
    if ($fixcoursesortorder) {
        fix_course_sortorder();
    }

    // purge appropriate caches in case fix_course_sortorder() did not change anything
    cache_helper::purge_by_event('changesincourse');
    if ($changesincoursecat) {
        cache_helper::purge_by_event('changesincoursecat');
    }

    // Test for and remove blocks which aren't appropriate anymore
    blocks_remove_inappropriate($course);

    // Save any custom role names.
    save_local_role_names($course->id, $data);

    // update enrol settings
    enrol_course_updated(false, $course, $data);

    // Update course tags.
    if (isset($data->tags)) {
        core_tag_tag::set_item_tags('core', 'course', $course->id, context_course::instance($course->id), $data->tags);
    }

    if ($oldcourse->format !== $course->format) {
        // Remove all options stored for the previous format
        // We assume that new course format migrated everything it needed watching trigger
        // 'course_updated' and in method format_XXX::update_course_format_options()
        $DB->delete_records('course_format_options',
                array('courseid' => $course->id, 'format' => $oldcourse->format));
    }
}

$filepath = "$csv_path/degreed_content.csv";
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read('') as $row) {
    $solution_id = $row['solutionid'];

    if($row['lmstype'] == 'trail'){
        mtrace("Pulando trilha $solution_id");
        continue;
    }

    try {
        $curso = curso::get_by_codcurso($solution_id);
        if(empty($curso)){
            mtrace("Curso $solution_id não encontrado!");
            continue;
        }

        $idcurso = $curso->get('idcurso');

        $courseid = $DB->get_field('course', 'id', ['idnumber' => $idcurso]);
        if(!$courseid){
            mtrace("Curso $idcurso não encontrado!");
            continue;
        }

        $course = (object) ['id' => $courseid];
        $key = "customfield_cadastrar_na_degreed";
        $course->$key = 1;
        custom_update_course($course);

        $sync_record = course_sync_record::get_or_create_from_courseid($courseid);
        $sync_record->set_many([
            'enabled' => 1,
            'localid' => $idcurso,
            'externalid' => $row['degreedid'],
            'timechanged' => 0,
        ]);
        $sync_record->save();

        mtrace("Curso $idcurso configurado!");
        $counter++;
        
    } catch (\Throwable $th) {
        mtrace_exception($th);
    }
}

print_r("\nIMPORTADOS: $counter");