<?php

require_once(__DIR__ . '/config.php');

$filepath = "$csv_path/notas-itens-trilhas.csv";
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read('') as $row) {
    try {
        if(\local_legacy\models\item_trilha_aluno::upsert_from_csv($row)){
            $counter++;
            mtrace('.','');
        }
    } catch (\Throwable $th) {
        mtrace_exception($th);
    }
}

print_r("\nIMPORTADOS: $counter");