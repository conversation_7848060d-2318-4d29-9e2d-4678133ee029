<?php namespace local_sgf_rm_integration\models;

use \local_sgf_rm_integration\models\interfaces\user_interface;
use \local_sgf_rm_integration\util\traits\web_service_format_trait;
use \local_ssystem\constants\custom_profile_fields;

use tool_companymanagement\repositories\company_repository;
use tool_companymanagement\services\company_service;

require_once($CFG->dirroot . '/user/profile/lib.php');
require_once($CFG->dirroot . '/user/lib.php');

class abstract_user implements user_interface {

    use web_service_format_trait;

    const UNIDENTIFIED = 'NÃO IDENTIFICADO';
    const HASH_FIELD = 'sgf_rm_hash';

    const ORIGIN = null;

    protected object $user;
    protected string $hash;
    protected static company_service $company_service;

    protected function __construct(object $user) {
        $this->user = $user;

        if(!isset(self::$company_service)){
            self::$company_service = new company_service(new company_repository());
        }
    }

    public static function get_by_username(string $username) : ?static {
        global $DB;

        if($user = $DB->get_record('user', ['username' => $username])){
            return static::from_user_object($user);
        }

        return null;
    }

    public static function get_by_cpf(string $cpf) : ?static {
        $cpf = self::extract_only_numbers($cpf);
        return static::get_by_username($cpf);
    }

    public static function from_user_object(object $user) : static {
        if(!static::is_profile_data_loaded($user)){
            profile_load_data($user);
        }

        return new static($user);
    }

    protected static function clean_email(array $data) : string {
        if(empty($data['EMAIL'])){
            $cpf = self::extract_only_numbers($data['CPF']);
            return "$<EMAIL>";
        }

        return clean_param($data['EMAIL'], PARAM_EMAIL);
    }

    public static function from_api(array $data) : static {
        global $CFG;
        
        $hash = static::hash_api_data($data);

        $names = explode(' ', $data['NOME'] ?? "", 2);
        $cpf = self::extract_only_numbers($data['CPF']);

        $status_type = self::format_status_type($data['SITUACAOUSUARIO']);

        $lang = get_string_manager()->translation_exists('pt_br') ? 'pt_br' : 'en';

        $instance = new static((object)[
            'username' => $cpf,
            'firstname' => self::format_ucfirst_lowercase($names[0]),
            'lastname' => self::format_ucwords($names[1] ?? ''),
            'alternatename' => $data['NOMEEXIBICAO'] ?? '',
            'email' => static::clean_email($data),
            'confirmed' => intval($status_type != custom_profile_fields::STATUS_TYPE_PENDING),
            'suspended' => intval($status_type == custom_profile_fields::STATUS_TYPE_INACTIVE),
            'country' => 'BR',
            'lang' => $lang,
            'auth' => 'amei',
            'mnethostid' => $CFG->mnet_localhost_id,
            self::HASH_FIELD => $hash,
        ]);

        // Custom fields
        if(!empty($data['SEXO'])){
            $gender = self::format_gender($data['SEXO']);
            $instance->set_profile_field(custom_profile_fields::GENDER_FIELD, $gender);
        }

        if(!empty($data['DATANASCIMENTO'])){
            $birthdate = self::date_to_timestamp($data['DATANASCIMENTO']);
            $instance->set_profile_field(custom_profile_fields::BIRTHDATE_FIELD, $birthdate);
        }

        if(!empty($data['UF'])){
            $sebraeuf = 'SEBRAE/' . $data['UF'];
            $instance->set_profile_field(custom_profile_fields::UFSEBRAE_FIELD, $sebraeuf);
            $instance->set_profile_field(custom_profile_fields::STATE_FIELD, $data['UF']);
        }

        if(!empty($data['EMAILSECUNDARIO']) && str_contains($data['EMAILSECUNDARIO'], '@')){
            $instance->set_profile_field(custom_profile_fields::SECONDARY_EMAIL_FIELD, $data['EMAILSECUNDARIO']);
        }

        if(!empty($data['TELCELULAR'])){
            $cellphone = self::extract_only_numbers($data['TELCELULAR']);
            $instance->set_profile_field(custom_profile_fields::CELLPHONE_FIELD, "+55" . $cellphone);
        }

        if(!empty($data['DATACADASTROSISTEMA'])){
            $date = self::date_to_timestamp($data['DATACADASTROSISTEMA']);
            $instance->set_profile_field(custom_profile_fields::REGISTRATION_DATE_FIELD, $date);
        }

        // SITUACAOUSUARIO
        $instance->set_profile_field(custom_profile_fields::STATUS_TYPE_FIELD, $status_type);

        if(!empty($data['PERFILOCUPACIONAL'])){
            $profile = self::format_occupational_profile($data['PERFILOCUPACIONAL']);
            $instance->set_profile_field(custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD, $profile);
        }

        // CNPJ_VINCULO
        if(!empty($data['CNPJ_VINCULO'])){
            $cnpjs = self::$company_service->validate_cnpjs_and_create_missing_companies($data['CNPJ_VINCULO']);
            $instance->set_profile_field(custom_profile_fields::RELATED_COMPANY_FIELD, $cnpjs);
        }

        // CARGO
        if(!empty($data['CARGO'])){
            $position = trim($data['CARGO']);
            $instance->set_profile_field(custom_profile_fields::POSITION_FIELD, $position);
        }

        // FUNCAO
        if(!empty($data['FUNCAO'])){
            $role = trim($data['FUNCAO']);
            $instance->set_profile_field(custom_profile_fields::ROLE_FIELD, $role);
        }

        // ORIGEM
        if(!empty($data['ORIGEM'])){
            // $origin = trim($data['ORIGEM']);
            $origin = static::ORIGIN;
            $instance->set_profile_field(custom_profile_fields::ORIGIN_FIELD, $origin);
        }
        
        return $instance;
    }

    public function save() : static {
        $existing = $this->get_userid_and_hash_from_username($this->user->username);

        if(empty($existing)){
            if (!defined('PHPUNIT_TEST') || !PHPUNIT_TEST){
                mtrace("Inserting {$this->user->username}");
            }
            return $this->create();
        }

        if(empty($this->user->id)){
            $this->user->id = $existing->id;
        }

        if($this->user->id !== $existing->id){
            throw new \coding_exception("User ID mismatch for username '{$this->user->username}'.");
        }

        $hash_field = self::HASH_FIELD;
        $has_changed = $existing->$hash_field !== $this->user->$hash_field;
        if($has_changed){
            if (!defined('PHPUNIT_TEST') || !PHPUNIT_TEST){
                mtrace("updating {$this->user->username}");
            }
            return $this->update();
        }

        return $this;
    }

    protected function get_userid_from_username(string $username) : ?int {
        global $DB;
        return $DB->get_field('user', 'id', ['username' => $username]) ?: null;
    }

    protected function get_userid_and_hash_from_username(string $username) : ?object {
        global $DB;
        return $DB->get_record('user', ['username' => $username], 'id,' . self::HASH_FIELD) ?: null;
    }

    protected static function is_profile_data_loaded(object $user) : bool {
        foreach (get_object_vars($user) as $property => $value) {
            if (strpos($property, 'profile_field_') === 0) {
                return true;
            }
        }
        return false;
    }

    protected function create() : static {
        $this->user->id = user_create_user($this->user, false, false);
        $this->user->password = null;
        profile_save_data($this->user);

        \core\event\user_created::create_from_userid($this->user->id)->trigger();

        return $this;
    }

    protected function update() : static {
        profile_save_data($this->user);
        user_update_user($this->user, false, true);
        return $this;
    }

    public function __get(string $name) {
        if (property_exists($this->user, $name)) {
            return $this->user->$name;
        }
        return null;
    }

    public function __set(string $name, $value) {
        $this->user->$name = $value;
    }

    public function get_profile_field(string $shortname) : mixed {
        $key = "profile_field_$shortname";
        if (property_exists($this->user, $key)) {
            return $this->user->$key;
        }
        return null;
    }

    public function set_profile_field(string $shortname, mixed $value) : static {
        $key = "profile_field_$shortname";
        $this->user->$key = $value;
        return $this;
    }

    public static function hash_api_data(array $data): string {
        static $algo = null;
        if($algo === null){
            $algo = in_array('xxh3', hash_algos(), true) ? 'xxh3' : 'md5';
        }
    
        ksort($data);
        $json = json_encode($data);
        return hash($algo, $json);
    }
}
