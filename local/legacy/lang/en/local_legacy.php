<?php

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Repositório de dados legados (EADTECH)';
$string['legacy:editcertificates'] = 'Editar certificados legados';
$string['legacy:viewreports'] = "Visualizar dados legados";

// Certificate form headings and buttons
$string['certificate:editcertificate']            = 'Create/Edit Certificate';
$string['certificate:createanewcertificate']      = 'Create a New Certificate';
$string['certificate:editexistingcertificate']    = 'Edit Certificate: {$a}';
$string['certificate:certificatesaved']           = 'Certificate saved successfully.';
$string['certificate:cancel']                     = 'Cancel';
$string['certificate:savechanges']                = 'Save Changes';

// General section
$string['certificate:general']                    = 'General';
$string['certificate:certificatename']            = 'Certificate Name';
$string['certificate:intro']                      = 'Description';
$string['certificate:err_required']               = 'This field is required.';
$string['certificate:invalidcertificateid']       = 'Invalid certificate ID.';

// Design/Layout options
$string['certificate:designoptions']              = 'Design Options';
$string['certificate:certificateimage']           = 'Background Image (First Page)';
$string['certificate:certificatetext']            = 'Certificate Text (HTML)';
$string['certificate:width']                      = 'Page Width (mm)';
$string['certificate:height']                     = 'Page Height (mm)';
$string['certificate:certificatetextx']           = 'Text Position X (mm)';
$string['certificate:certificatetexty']           = 'Text Position Y (mm)';
$string['certificate:error_invalidwidth']         = 'Width must be greater than zero.';
$string['certificate:error_invalidheight']        = 'Height must be greater than zero.';
$string['certificate:error_invalidposition']      = 'Position must be zero or greater.';

// Second page options
$string['certificate:secondpageoptions']          = 'Second Page Options';
$string['certificate:enablesecondpage']           = 'Enable Second Page';
$string['certificate:secondimage']                = 'Background Image (Second Page)';
$string['certificate:secondpagetext']             = 'Second Page Text (HTML)';
$string['certificate:secondpagex']                = 'Second Page Text X (mm)';
$string['certificate:secondpagey']                = 'Second Page Text Y (mm)';

// QR code section
$string['certificate:qrcodeoptions']              = 'QR Code Options';
$string['certificate:printqrcode']                = 'Print QR Code';
$string['certificate:codex']                      = 'QR Code Position X (mm)';
$string['certificate:codey']                      = 'QR Code Position Y (mm)';
$string['certificate:qrcodefirstpage']            = 'Print QR Code on First Page';

// Filemanager and editor labels
$string['certificate:description']                = 'Description';
$string['certificate:certificateimage_help']      = 'Upload a background image (PDF, JPEG, PNG) that will appear on the first page of the certificate.';
$string['certificate:certificatetext_help']       = 'Enter the main certificate text using HTML. You can embed TAG placeholders such as [NOME_ALUNO], [CARGA_HORARIA], etc.';
$string['certificate:secondimage_help']           = 'Upload a background image (PDF, JPEG, PNG) for the second page of the certificate.';
$string['certificate:secondpagetext_help']        = 'Enter the text that appears on the second page (HTML). Use TAG placeholders as needed.';
$string['certificate:width_help']                 = 'The width of the certificate page in millimeters.';
$string['certificate:height_help']                = 'The height of the certificate page in millimeters.';
$string['certificate:certificatetextx_help']      = 'Horizontal (X) position in millimeters where the main text block starts.';
$string['certificate:certificatetexty_help']      = 'Vertical (Y) position in millimeters where the main text block starts.';
$string['certificate:secondpagex_help']           = 'Horizontal (X) position in millimeters for the second page text.';
$string['certificate:secondpagey_help']           = 'Vertical (Y) position in millimeters for the second page text.';
$string['certificate:printqrcode_help']           = 'If enabled, a QR code will be printed on the certificate.';
$string['certificate:codex_help']                 = 'Horizontal (X) position in millimeters where the QR code will be placed.';
$string['certificate:codey_help']                 = 'Vertical (Y) position in millimeters where the QR code will be placed.';
$string['certificate:qrcodefirstpage_help']       = 'If enabled, the QR code will appear on the first page; otherwise, it appears on the back or second page.';


$string['reports:select_a_report'] = "Selecione um relatório";
$string['reports:index_title'] = "Dados legados (EADTEC)";
$string['reports:index_heading'] = "Relatórios de dados legados";


// Validation

$string['certificateverification'] = 'Validação de Certificado';
$string['entercode'] = 'Digite o código do certificado';
$string['certificatecode'] = 'Código do certificado';
$string['verify'] = 'Verificar';
$string['certificatevalid'] = 'Certificado válido';
$string['invalidcertificate'] = 'Certificado inválido';