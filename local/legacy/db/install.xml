<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/legacy/db" VERSION="20250522" COMMENT="XMLDB file for Moodle report/eadtech"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
<TABLES>
    <TABLE NAME="legacy_certificate" COMMENT="EADTECH Certificates">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="solutiontype" TYPE="char" LENGTH="8" NOTNULL="true" COMMENT="course/trail" SEQUENCE="false"/>
        <FIELD NAME="solutionid" TYPE="int" LENGTH="10" NOTNULL="false" COMMENT="codcurso ou codtrilha" SEQUENCE="false"/>
        <FIELD NAME="legacyid" TYPE="int" LENGTH="10" NOTNULL="false" COMMENT="ID do certificado original" SEQUENCE="false"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="width" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="297" SEQUENCE="false"/>
        <FIELD NAME="height" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="210" SEQUENCE="false"/>
        <FIELD NAME="certificatetext" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="certificatetextformat" TYPE="int" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="certificatetextx" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="certificatetexty" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="50" SEQUENCE="false"/>        
        <FIELD NAME="printqrcode" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
        <FIELD NAME="qrcodefirstpage" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="codex" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="codey" TYPE="int" LENGTH="4" NOTNULL="true" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="enablesecondpage" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="secondpagex" TYPE="int" LENGTH="4" NOTNULL="false" DEFAULT="10" SEQUENCE="false"/>
        <FIELD NAME="secondpagey" TYPE="int" LENGTH="4" NOTNULL="false" DEFAULT="50" SEQUENCE="false"/>
        <FIELD NAME="secondpagetext" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="secondpagetextformat" TYPE="int" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
    </TABLE>
    <TABLE NAME="legacy_certificate_issues" COMMENT="EADTECH Certificate issues">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="certificateid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="codsituacaoalunocurso" TYPE="int" LENGTH="10" NOTNULL="true" COMMENT="legacy_situacao_curso" SEQUENCE="false"/>
        <FIELD NAME="codsituacao" TYPE="int" LENGTH="10" NOTNULL="true" COMMENT="legacy_situacao_trilha" SEQUENCE="false"/>
        <FIELD NAME="code" TYPE="char" LENGTH="80" NOTNULL="true" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="validation_idx" UNIQUE="false" FIELDS="code"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_turmas_cursos" COMMENT="Turmas de cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="nometurma" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="numeromaximoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="numerominimoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="disponivel" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="descricao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cargahoraria" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
        <FIELD NAME="datainicioprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafimprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="cancelada" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="false" FIELDS="codturma,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_turmas_trilhas" COMMENT="Turmas de trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Id"/>
        <FIELD NAME="codturma_geral" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="TURMA.CODTURMA related when TURMA.ActivityCollectionClassId = TRN_ActivityCollectionClasses.id"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ActivityCollectionId"/>
        <FIELD NAME="nometurma" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="name"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Period_From"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Period_To"/>
        <FIELD NAME="numeromaximoalunos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="Vacancies"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="RegistrationDate"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="ModificationDate"/>
        <FIELD NAME="disponivel" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Active"/>
        <FIELD NAME="descricao" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="Description"/>
        <FIELD NAME="datainicioprematricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="PreSubscription_Period_From"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="false" FIELDS="codturma,hash"/>
        <INDEX NAME="codturma_geral_idx" UNIQUE="false" FIELDS="codturma_geral"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_situacao_curso" COMMENT="Situacao de alunos em cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codsituacaoalunocurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datamatricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="percconclusao" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="percaproveitamento" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="prazodeacesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="origemmatricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="statusmatricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="progressstatus" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="data_de_cancelamento" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="motivo_do_cancelamento" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="nota_do_usuario" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="data_do_primeiro_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="data_do_ultimo_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="data_de_conclusao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_idx" UNIQUE="false" FIELDS="codturma"/>
        <INDEX NAME="codcurso_idx" UNIQUE="false" FIELDS="codcurso,codaluno,hash,codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_situacao_trilha" COMMENT="Situacao de alunos em trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codsituacao" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="studentClass.Id"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="studentClass.ActivityCollectionId"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="studentClass.ActivityCollectionClassId"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datamatricula" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.RegistrationDate"/>
        <FIELD NAME="percconclusao" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_PercentConcluded"/>
        <FIELD NAME="percaproveitamento" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_PercentGrade"/>
        <FIELD NAME="prazodeacesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_From"/>
        <FIELD NAME="origemmatricula" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false" COMMENT="studentClass.EnrollmentOrigin"/>
        <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_From"/>
        <FIELD NAME="datafim" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.Period_To"/>
        <FIELD NAME="statusmatricula" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" COMMENT="studentClass.state"/>
        <FIELD NAME="progressstatus" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false" COMMENT="Status de progresso"/>
        <FIELD NAME="nota_do_usuario" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2" COMMENT="studentClass.TrainingState_AccessedOn"/>
        <FIELD NAME="data_do_primeiro_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_FirstAccess"/>
        <FIELD NAME="data_do_ultimo_acesso" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_AccessedOn"/>
        <FIELD NAME="data_de_conclusao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="studentClass.TrainingState_ConcludedOn"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codsituacao_idx" UNIQUE="false" FIELDS="codsituacao"/>
        <INDEX NAME="codtrilha_idx" UNIQUE="false" FIELDS="codtrilha,codaluno,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente" COMMENT="Usuários do corpo docente">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nome" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="email" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="cargo" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="telefone" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="celular" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cpf" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="uf" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="coordenador" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="tran_userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_idx" UNIQUE="true" FIELDS="codcorpo,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente_curso" COMMENT="Relacionamento de corpo docente com turmas de cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="dataalocacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="cargo" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_turma_idx" UNIQUE="false" FIELDS="codcorpo,codturma,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_corpo_docente_trilha" COMMENT="Relacionamento de corpo docente com turmas de trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="[dbo].LMS.TRN_ActivityCollectionClasses.ID"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="[dbo].LMS.TRN_Activities.Id"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codcorpo_turma_idx" UNIQUE="false" FIELDS="codcorpo,codturma,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_empresas" COMMENT="Empresas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codempresa" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="razao_social" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cnpj" TYPE="char" LENGTH="14" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="estado" TYPE="int" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datacadastro" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="dataabertura" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="dataalteracao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="excluido" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="fornecedorsolucoes" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="nome_fantasia" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codempresa_idx" UNIQUE="false" FIELDS="codempresa,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_empresa_aluno" COMMENT="Relacionamento de empresas com alunos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codempresa" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="excluido" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="empresaprincipal" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datainclusao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datamodificacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codaluno_empresa_idx" UNIQUE="false" FIELDS="codaluno,codempresa,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_alunos" COMMENT="Alunos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nome" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="email" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="estado" TYPE="char" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="sexo" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="nascimento" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="escolaridade" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="estadocivil" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datacadastro" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="nivelocupacional" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="cpf" TYPE="char" LENGTH="11" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="emailsecundario" TYPE="char" LENGTH="120" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="celular" TYPE="char" LENGTH="20" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="perfil" TYPE="char" LENGTH="60" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="status" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="filial" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="dataadmissao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="dataexpiracao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="dataalteracao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="ufsebrae" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datamodificacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codaluno_idx" UNIQUE="false" FIELDS="codaluno,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_segmentos" COMMENT="Segmentos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codsegmento" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nomesegmento" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codsegmento_idx" UNIQUE="false" FIELDS="codsegmento,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_segmento_aluno" COMMENT="Relacionamento de segmentos com alunos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codsegmento" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="tipo" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="principal" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codaluno_segmento_idx" UNIQUE="false" FIELDS="codaluno,codsegmento,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_segmento_turma_curso" COMMENT="Relacionamento de segmentos com turmas de cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codsegmento" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codprogramaturma" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_segmento_idx" UNIQUE="false" FIELDS="codturma,codsegmento,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_segmento_turma_trilha" COMMENT="Relacionamento de segmentos com turmas de trilhas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codsegmento" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codturma_segmento_idx" UNIQUE="false" FIELDS="codturma,codsegmento,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_trabalhos" COMMENT="Tabela importada de TRABALHOS do sistema legado">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codtrabalho" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codcorpo" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="descricao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="dataentrega" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="permiteatraso" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="datahora" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="suplemento" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="titulo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="tipotrabalho" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="pontuacao" TYPE="number" LENGTH="5" DECIMALS="2" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="permitirreenvio" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="atividadeestruturada" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="tipoaprovacao" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="criadoem" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="datainicio" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codtrabalho_idx" UNIQUE="false" FIELDS="codtrabalho,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_trabalhos_alunos" COMMENT="Tabela importada de TRABALHOS_ALUNOS do sistema legado">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codtrabalhosusuarios" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codtrabalho" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="arquivo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="observacoes" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="status" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="entregue" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="datafinal" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="pontosold" TYPE="number" LENGTH="5" DECIMALS="2" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="consideracoes" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="nomelink_aluno" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="nomelink_corrigido" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="pontos" TYPE="number" LENGTH="5" DECIMALS="2" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="responsaveltrabalhoemgrupo" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codtrabalhogrupo" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="arquivocorrigido" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="datadivulgacaonotas" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="nrsacorigem" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codtrabalhosusuarios_idx" UNIQUE="false" FIELDS="codtrabalhosusuarios,hash"/>
        <INDEX NAME="codtrabalho_aluno_idx" UNIQUE="false" FIELDS="codtrabalho,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_feedback_v1" COMMENT="Feedback legado consolidado de Usuario_Feedback e PerRes_Feedback">
        <FIELDS>
            <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
            <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="codperresfeedback" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="resposta" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="datahora" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="justificativa" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            <FIELD NAME="pergunta" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="pesopergunta" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="codgrupofeedback" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="ordem" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
        </FIELDS>
        <KEYS>
            <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        </KEYS>
        <INDEXES>
        <INDEX NAME="codcurso_aluno_idx" UNIQUE="false" FIELDS="codaluno,codcurso,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_feedback_cursos" COMMENT="Feedback v2 para cursos">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codfeedbackrespostadeusuario" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codfeedback" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="descricao" TYPE="char" LENGTH="40" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="textointroducao" TYPE="char" LENGTH="250" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codtopico" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codpergunta" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="pergunta" TYPE="char" LENGTH="150" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codcategoriaperg" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="tipopergunta" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="modulo" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="resposta" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codrespostadepergunta" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="justificativa" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="dtresposta" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codfeedbackrespostadeusuario_idx" UNIQUE="true" FIELDS="codfeedbackrespostadeusuario,hash"/>
        <INDEX NAME="codcurso_aluno_idx" UNIQUE="false" FIELDS="codcurso,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_feedback_trilhas" COMMENT="Feedback v2 para trilhas">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codfeedbackrespostadeusuario" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codfeedback" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="descricao" TYPE="char" LENGTH="40" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="textointroducao" TYPE="char" LENGTH="250" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codtopico" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codpergunta" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="pergunta" TYPE="char" LENGTH="150" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codcategoriaperg" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="tipopergunta" TYPE="char" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="modulo" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="resposta" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codrespostadepergunta" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="justificativa" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="dtresposta" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codfeedbackrespostadeusuario_idx" UNIQUE="true" FIELDS="codfeedbackrespostadeusuario,hash"/>
        <INDEX NAME="codtrilha_aluno_idx" UNIQUE="false" FIELDS="codtrilha,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_topicos" COMMENT="Topicos de curso">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codtopico" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codtopicopai" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="codsala" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="nome" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="identifier" TYPE="char" LENGTH="90" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="ordem" TYPE="int" LENGTH="5" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="profundidade" TYPE="int" LENGTH="5" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="criado" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="modificado" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="pontuavel" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="bloqueiatopico" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="tipopontuavel" TYPE="char" LENGTH="3" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="refazer" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="qtdquestoes" TYPE="int" LENGTH="5" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
          <FIELD NAME="compoeaproveitamento" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="indisponivelporcheckup" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="maxtentativas" TYPE="int" LENGTH="5" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="tiposorteio" TYPE="int" LENGTH="5" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
          <FIELD NAME="cargahoraria" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
          <FIELD NAME="resorteioquestoes" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="mostrarevisao" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="mostracorrecao" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="msginicioavaliacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="msgfimavaliacao" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="pagar" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="temsorteio" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="bloquearrecursos" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="avorientadatopico" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="masteryscore" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
          <FIELD NAME="valor" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
          <FIELD NAME="codtopicoorigem" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="obrigatorio" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codtopico_idx" UNIQUE="true" FIELDS="codtopico,hash"/>
        <INDEX NAME="notas_idx" UNIQUE="true" FIELDS="codtopico,pontuavel,compoeaproveitamento"/>
        <INDEX NAME="codcurso_turma_idx" UNIQUE="false" FIELDS="codcurso,codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_topicos_realizados" COMMENT="Topicos realizados">
      <FIELDS>
          <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
          <FIELD NAME="codtopicorealizado" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codtopico" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codcurso" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="nota" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
          <FIELD NAME="tempoacessosegundos" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="corelesson_status" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
          <FIELD NAME="ultimoacesso" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="primeiroacesso" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="numacessos" TYPE="int" LENGTH="5" NOTNULL="false" SEQUENCE="false"/>
          <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
          <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codtopicorealizado_idx" UNIQUE="true" FIELDS="codtopicorealizado,hash"/>
        <INDEX NAME="codtopico_aluno_idx" UNIQUE="false" FIELDS="codtopico,codaluno,codturma"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_gestores" COMMENT="Gestores">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codusuariogestor" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nomecompleto" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="email" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="login" TYPE="char" LENGTH="196" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="datadecriacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="datamodificacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="status" TYPE="char" LENGTH="30" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="tran_userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="corpodocente" TYPE="int" LENGTH="1" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codgestor_idx" UNIQUE="true" FIELDS="codusuariogestor,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_notas_forums" COMMENT="Notas dos usuários em fóruns">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="codnotatopicousuario" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codtopico" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="nome_avaliador" TYPE="char" LENGTH="90" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codcorpo_avaliador" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="codgestor_avaliador" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="datacriacao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="dataalteracao" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="valornota" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" DECIMALS="5"/>
        <FIELD NAME="feedback" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="nome_autor_feedback" TYPE="char" LENGTH="90" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codcorpo_autor_feedback" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="codgestor_autor_feedback" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="codnotatopicousuario_idx" UNIQUE="true" FIELDS="codnotatopicousuario,hash"/>
        <INDEX NAME="codtopico_aluno_idx" UNIQUE="false" FIELDS="codtopico,codaluno"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_itens_trilhas" COMMENT="Itens de trilhas (estranhamente inclui trilhas)">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="coditem" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="description" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="active" TYPE="int" LENGTH="1" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
        <FIELD NAME="activitygroupid" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="keywords" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="updatedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="registrationdate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>   
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="coditem_idx" UNIQUE="false" FIELDS="coditem,hash"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="legacy_itens_trilhas_alunos" COMMENT="Notas e conclusão de itens de trilhas por alunos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="coditem" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codturma" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codaluno" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="codtrilha" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="porcentagem_conclusao" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="nota" TYPE="number" LENGTH="5" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
        <FIELD NAME="hash" TYPE="char" LENGTH="32" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="identifier_idx" UNIQUE="true" FIELDS="coditem,codturma,codaluno,hash"/>
        <INDEX NAME="trilha_idx" UNIQUE="false" FIELDS="codtrilha,coditem"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>