<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Alunos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/alunos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'alunos';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\aluno::class
);

// Let the user download if requested.
$report->is_downloading($download, $tableid . '_export', $tableid);

// Omit any internal or unwanted columns.
$report->omit_columns(['id', 'timemodified', 'timecreated', 'usermodified', 'hash']);

$report->register_formatter('nascimento', 'legacy_format_date');
$report->register_formatter('datacadastro', 'legacy_format_date');
$report->register_formatter('dataalteracao', 'legacy_format_bool');
$report->register_formatter('datamodificacao', 'legacy_format_bool');

$report->define_baseurl($PAGE->url);
// $report->set_filters(['status' => 'active']);
// $report->set_sort('id', 'ASC');

// Only output header if not exporting.
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// Render the table (25 rows per page, no initials bar).
$report->out(25, false);

// Footer for HTML view.
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
