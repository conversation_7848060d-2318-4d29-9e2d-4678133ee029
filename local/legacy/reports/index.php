<?php

require_once(__DIR__ . '/../../../config.php');
$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/index.php'));
$PAGE->set_title(get_string('reports:index_title', 'local_legacy'));
$PAGE->set_heading(get_string('reports:index_heading', 'local_legacy'));

$reports = [
    'alunos.php'                         => 'Alunos',
    'certificados_emitidos_de_cursos.php'=> 'Certificados Emitidos de Cursos',
    'certificados_emitidos_de_trilhas.php'=> 'Certificados Emitidos de Trilhas',
    'corpos_docentes.php'               => 'Corpos Docentes',
    'corpos_docentes_de_cursos.php'     => 'Docentes de Cursos',
    'corpos_docentes_de_trilhas.php'    => 'Docentes de Trilhas',
    'cursos.php'                         => 'Cursos',
    'empresas.php'                      => 'Empresas',
    'empresas_de_alunos.php'            => 'Empresas de Alunos',
    'feedback_de_cursos.php'            => 'Feedback de Cursos (Atual)',
    'feedback_de_cursos_v1.php'         => 'Feedback de Cursos (v1)',
    'feedback_de_trilhas.php'           => 'Feedback de Trilhas',
    'gestores.php'                      => 'Gestores',
    'itens_de_trilhas_realizados.php'   => 'Itens de trilhas realizados',
    'segmentos.php'                     => 'Segmentos',
    'segmentos_de_alunos.php'           => 'Segmentos de Alunos',
    'segmentos_de_turmas_de_cursos.php' => 'Segmentos de Turmas de Cursos',
    'segmentos_de_turmas_de_trilhas.php'=> 'Segmentos de Turmas de Trilhas',
    'situacao_dos_alunos_em_cursos.php' => 'Situação dos Alunos em Cursos',
    'situacao_dos_alunos_em_trilhas.php'=> 'Situação dos Alunos em Trilhas',
    'topicos_realizados'                => 'Tópicos realizados',
    'trabalhos.php'                     => 'Trabalhos',
    'trabalhos_de_alunos.php'           => 'Trabalhos de Alunos',
    'trilhas.php'                       => 'Trilhas',
    'turmas_de_cursos.php'              => 'Turmas de Cursos',
    'turmas_de_trilhas.php'             => 'Turmas de Trilhas',
];

asort($reports);

echo $OUTPUT->header();
echo $OUTPUT->heading(get_string('reports:select_a_report', 'local_legacy'));

echo html_writer::start_tag('ul');

foreach ($reports as $file => $title) {
    $url = new moodle_url("/local/legacy/reports/{$file}");
    echo html_writer::tag('li', html_writer::link($url, $title));
}

echo html_writer::end_tag('ul');
echo $OUTPUT->footer();
