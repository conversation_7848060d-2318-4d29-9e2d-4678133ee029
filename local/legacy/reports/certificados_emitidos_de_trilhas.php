<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Certificados emitidos de trilhas";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/certificados_emitidos_de_trilhas.php.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'certificados_emitidos_de_trilhas.php';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'codigo_verificacao',
    'nome_aluno',
    'nome_curso',
    'nome_turma',
    'percconclusao',
    'percaproveitamento',
    'statusmatricula',
    'progressstatus',
    'data_de_conclusao',
]);

$report->define_headers([
    'Código do certificado',
    "Aluno",
    'Curso',
    'Turma',
    '% Conclusão',
    '% Aproveitamento',
    'Status da Matrícula',
    'Status de Progresso',
    'Conclusão',
]);

$report->set_sql(
    "mloci.id,
     mloci.code AS codigo_verificacao,
     mla.nome AS nome_aluno,
     mlc.nome AS nome_trilha,
     mltc.nometurma AS nome_turma,
     mlsc.percconclusao,
     mlsc.percaproveitamento,
     mlsc.statusmatricula,
     mlsc.progressstatus,
     mlsc.data_de_conclusao",
    "{legacy_certificate_issues} mloci 
     LEFT JOIN {legacy_situacao_trilha} mlsc ON mlsc.codsituacao  = mloci.codsituacaoalunocurso 
     LEFT JOIN {legacy_alunos} mla ON mla.codaluno = mlsc.codaluno
     LEFT JOIN {legacy_trilhas} mlc ON mlc.codtrilha = mlsc.codtrilha 
     LEFT JOIN {legacy_turmas_trilhas} mltc ON mltc.codturma = mlsc.codturma",
    "WHERE codsituacao > 0"
);

$report->set_default_sort([
    'mloci.id' => 'ASC',
]);

$report->sortable(false);

// register formatters
$report->register_formatter('data_de_conclusao',     'legacy_format_date');

// formatadores
$report->register_formatter('datahora', 'legacy_format_date');
$report->register_formatter('justificativa', 'legacy_format_truncate_html');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
