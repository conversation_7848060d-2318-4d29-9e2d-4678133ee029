<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = 'Tópicos realizados';
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/topicos_realizados.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'topicos_realizados';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . '_export', $tableid);

// define columns and headers
$report->define_columns([
    'id',
    'nome_curso',
    'nome_topico',
    'nome_turma',
    'nome_aluno',
    'identificador_topico',
    'obrigatorio',
    'nota',
    'status',
    'primeiroacesso',
    'ultimoacesso',
]);
$report->define_headers([
    'ID',
    'Curso',
    'Tópico',
    'Turma',
    'Aluno',
    'Identificador do tópico',
    'Obrigatório',
    'Nota',
    'Status',
    'Primeiro acesso',
    'Último acesso',
]);

$fields = "
    mltr.id,
    mlc.nome AS nome_curso,
    mlt.nome AS nome_topico,
    mltc.nometurma AS nome_turma,
    mla.nome AS nome_aluno,
    mlt.identifier AS identificador_topico,
    mlt.obrigatorio,
    mltr.nota,
    mltr.corelesson_status AS status,
    mltr.primeiroacesso,
    mltr.ultimoacesso";
$from = "
    {legacy_topicos_realizados} mltr
    JOIN {legacy_topicos} mlt ON (
        mltr.codtopico = mlt.codtopico
        AND mlt.compoeaproveitamento = 1
        AND mlt.pontuavel = 1
    )
    JOIN {legacy_alunos} mla ON mla.codaluno = mltr.codaluno
    LEFT JOIN {legacy_cursos} mlc ON mlc.codcurso = mlt.codcurso
    LEFT JOIN {legacy_turmas_cursos} mltc ON mltr.codturma = mltc.codturma";
$where = 'mlt.codcurso > 0';

$report->set_sql($fields, $from, $where);

// default sorting
$report->set_default_sort([
    'mltr.id' => 'DESC',
]);

// register formatters
$report->register_formatter('obrigatorio', 'legacy_format_bool');
$report->register_formatter('primeiroacesso', 'legacy_format_date');
$report->register_formatter('ultimoacesso', 'legacy_format_date');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
