<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title   = "Gestores";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/gestores.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'gestores';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\gestor::class
);

$report->is_downloading($download, $tableid . '_export', $tableid);

$report->omit_columns([
    'id',
    'codusuariogestor',
    'timemodified',
    'timecreated',
    'usermodified',
    'hash'
]);

$report->set_header_overrides([
    'nomecompleto'     => 'Nome Completo',
    'email'            => 'E-mail',
    'login'            => 'Login',
    'datadecriacao'    => 'Criado em',
    'datamodificacao'  => 'Modificado em',
    'status'           => 'Status',
    'corpodocente'     => 'Corpo Docente',
]);

$report->register_formatter('datadecriacao',   'legacy_format_date');
$report->register_formatter('datamodificacao', 'legacy_format_date');
$report->register_formatter('corpodocente', 'legacy_format_bool');


$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
