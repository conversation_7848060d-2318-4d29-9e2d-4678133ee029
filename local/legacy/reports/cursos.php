<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'cursos';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\curso::class
);

// Headers
$report->set_header_overrides([
    'courseid' => 'ID do curso (Moodle)',
    'codcurso' => 'Código do curso',
    'disponivel' => 'Disponível',
    'idcurso' => 'ID do curso',
    'nome' => 'Nome',
    'status' => 'Status',
    'prazo' => 'Prazo (dias)',
    'datacriacao' => 'Data de criação',
    'datamoficacao' => 'Data de modificação',
    'cursopresencial' => 'Curso presencial',
    'frequencia' => 'Frequência',
    'media' => 'Média',
    'keywords' => 'Palavras-chave',
    'cargahoraria' => 'Carga horária',
    'descricao' => 'Descrição',
    'tipo_solucao' => 'Tipo de solução',
    'apresentacao' => 'Apresentação',
    'objetivos' => 'Objetivos',
    'conteudo_programatico' => 'Conteúdo programático',
    'nivel_de_complexidade' => 'Nível de complexidade',
    'termo_de_aceite' => 'Termo de aceite',
    'ficha_tecnica' => 'Ficha técnica',
    'requisitos' => 'Requisitos',
    'criterios_de_avaliacao' => 'Critérios de avaliação',
    'publico' => 'Público-alvo',
    'area_subarea' => 'Área/Subárea',
]);


// Let the user download if requested.
$report->is_downloading($download, $tableid . '_export', $tableid);

// Omit any internal or unwanted columns.
$report->omit_columns(['id', 'timemodified', 'timecreated', 'usermodified', 'hash']);

$report->register_formatter('datacriacao', 'legacy_format_date');
$report->register_formatter('datamoficacao', 'legacy_format_date');
$report->register_formatter('cursopresencial', 'legacy_format_bool');
$report->register_formatter('disponivel', 'legacy_format_bool');
$report->register_formatter('courseid', 'legacy_format_course_url');
$report->register_formatter('cargahoraria', 'legacy_format_duration_hours');


if(!$report->is_downloading()){
    $report->register_formatter('apresentacao', 'legacy_format_truncate_html');
    $report->register_formatter('descricao', 'legacy_format_truncate_html');
    $report->register_formatter('objetivos', 'legacy_format_truncate_html');
    $report->register_formatter('conteudo_programatico', 'legacy_format_truncate_html');
    $report->register_formatter('nivel_de_complexidade', 'legacy_format_truncate_html');
    $report->register_formatter('termo_de_aceite', 'legacy_format_truncate_html');
    $report->register_formatter('ficha_tecnica', 'legacy_format_truncate_html');
    $report->register_formatter('requisitos', 'legacy_format_truncate_html');
    $report->register_formatter('criterios_de_avaliacao', 'legacy_format_truncate_html');
    $report->register_formatter('publico', 'legacy_format_truncate_html');
    $report->register_formatter('area_subarea', 'legacy_format_truncate_html');
}


$report->define_baseurl($PAGE->url);
// $report->set_filters(['status' => 'active']);
// $report->set_sort('id', 'ASC');

// Only output header if not exporting.
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// Render the table (25 rows per page, no initials bar).
$report->out(25, false);

// Footer for HTML view.
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
