<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Corpos docentes";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/corpos_docentes.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'corpos_docentes';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\corpo_docente::class
);

// Let the user download if requested.
$report->is_downloading($download, $tableid . '_export', $tableid);

// Omit any internal or unwanted columns.
$report->omit_columns(['id', 'timemodified', 'timecreated', 'usermodified', 'hash', 'tran_userid']);

$report->set_header_overrides([
    'codcorpo'     => 'Código do Corpo',
    'nome'         => 'Nome',
    'email'        => 'E-mail',
    'criado'       => 'Criado em',
    'modificado'   => 'Modificado em',
    'cargo'        => 'Cargo',
    'telefone'     => 'Telefone',
    'celular'      => 'Celular',
    'cpf'          => 'CPF',
    'uf'           => 'UF',
    'coordenador'  => 'Coordenador',
]);

$report->register_formatter('criado', 'legacy_format_date');
$report->register_formatter('modificado', 'legacy_format_date');
$report->register_formatter('coordenador', 'legacy_format_bool');

$report->define_baseurl($PAGE->url);

// Only output header if not exporting.
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// Render the table (25 rows per page, no initials bar).
$report->out(25, false);

// Footer for HTML view.
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
