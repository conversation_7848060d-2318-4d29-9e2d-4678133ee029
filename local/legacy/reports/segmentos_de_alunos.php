<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Segmentos de Alunos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/segmentos_de_alunos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'segmentos_de_alunos';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

// define columns and headers
$report->define_columns([
    'nome',
    'cpf',
    'userid',
    'nomesegmento',
    'tipo',
    'principal',
]);
$report->define_headers([
    'Nome',
    'CPF',
    'ID do Usuário (Moodle)',
    'Segmento',
    'Tipo',
    'Principal',
]);

// apply SQL
$report->set_sql(
    "mlsa.id,
     mla.nome AS nome,
     mla.cpf AS cpf,
     mla.userid AS userid,
     mle.nomesegmento AS nomesegmento,
     mlsa.tipo AS tipo,
     mlsa.principal AS principal",
    '{legacy_segmento_aluno} mlsa
     JOIN {legacy_alunos} mla ON mlsa.codaluno = mla.codaluno
     JOIN {legacy_segmentos} mle ON mle.codsegmento = mlsa.codsegmento',
    '1=1'
);

// default sorting
$report->set_default_sort([
    'mlsa.codaluno' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('userid', 'legacy_format_profile_url');
$report->register_formatter('principal', 'legacy_format_bool');
$report->register_formatter('tipo', 'legacy_format_truncate');
$report->register_formatter('nomesegmento', 'legacy_format_truncate');
$report->register_formatter('cpf', 'legacy_format_truncate');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
