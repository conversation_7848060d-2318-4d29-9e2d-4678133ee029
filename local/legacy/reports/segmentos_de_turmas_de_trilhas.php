<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Segmentos de Turmas de Trilhas";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/segmentos_de_turmas_trilhas.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'segmentos_de_turmas_trilhas';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nometurma',
    'nome_segmento',
    'nome_trilha',
    'datainicio',
    'datafim',
    'descricao',
]);

$report->define_headers([
    'Turma',
    'Segmento',
    'Trilha',
    'Início',
    'Fim',
    'Descrição',
]);

$report->set_sql(
    "mltt.nometurma,
     'Não existia na base da EADTECH' AS nome_segmento,
     mlt.nome AS nome_trilha,
     mltt.datainicio,
     mltt.datafim,
     mltt.descricao",
    "{legacy_segmento_turma_trilha} mlstt
     JOIN {legacy_turmas_trilhas} mltt ON mltt.codturma = mlstt.codturma
     JOIN {legacy_trilhas} mlt ON mlt.codtrilha = mltt.codtrilha",
    "1=1"
);

$report->set_default_sort([
    'mlstt.codsegmento' => 'ASC',
    'mlstt.codturma' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('descricao', 'legacy_format_truncate_html');


$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
