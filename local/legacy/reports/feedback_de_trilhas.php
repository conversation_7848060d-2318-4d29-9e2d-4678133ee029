<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Feedback de Trilhas pelos Alunos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/feedback_de_trilhas.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'feedback_de_trilhas';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_aluno',
    'nome_trilha',
    'nome_turma',
    'pergunta',
    'tipopergunta',
    'resposta',
    'justificativa',
    'dtresposta',
]);

$report->define_headers([
    '<PERSON><PERSON>',
    'Trilha',
    'Turma',
    'Pergunta',
    'Tipo de Pergunta',
    'Resposta',
    'Justificativa',
    'Data/Hora da Resposta',
]);

$report->set_sql(
    "mlft.id,
     mla.nome AS nome_aluno,
     mlt.nome AS nome_trilha,
     COALESCE(mltt.nometurma, mlft.codturma) AS nome_turma,
     mlft.pergunta,
     mlft.tipopergunta,
     mlft.resposta,
     mlft.justificativa,
     mlft.dtresposta",
    "{legacy_feedback_trilhas} mlft 
     JOIN {legacy_alunos} mla ON mla.codaluno = mlft.codaluno
     JOIN {legacy_trilhas} mlt ON mlt.codtrilha = mlft.codtrilha
     LEFT JOIN {legacy_turmas_trilhas} mltt ON mltt.codturma_geral = mlft.codturma",
    "1=1"
);

$report->set_default_sort([
    'mlft.codaluno'                         => 'ASC',
    'mlft.codtrilha'                        => 'ASC',
    'mlft.codturma'                         => 'ASC',
    'mlft.codpergunta'                      => 'ASC',
    'mlft.codfeedbackrespostadeusuario'     => 'ASC',
]);

$report->sortable(false);

$report->register_formatter('dtresposta',     'legacy_format_date');
$report->register_formatter('justificativa', 'legacy_format_truncate_html');
$report->register_formatter('resposta',      'legacy_format_truncate_html');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
