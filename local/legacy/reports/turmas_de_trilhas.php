<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Turmas de trilhas";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/turmas_de_trilhas.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'turmas_de_trilhas';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

// define columns and headers
$report->define_columns([
    'nometurma',
    'nome_trilha',
    'datainicio',
    'datafim',
    'datainicioprematricula',
    'criado',
    'modificado',
    'disponivel',
    'descricao',
]);
$report->define_headers([
    'Turma',
    'Trilha',
    'Início',
    'Fim',
    'Início Pré-Matrícula',
    'Criado em',
    'Modificado em',
    'Disponível',
    'Descrição',
]);

// apply SQL
$report->set_sql(
    "mltt.id,
     mltt.nometurma,
     mltt.datainicio,
     mltt.datafim,
     mltt.datainicioprematricula,
     mltt.criado,
     mltt.modificado,
     mltt.disponivel,
     mltt.descricao,
     mlt.nome AS nome_trilha",
    "{legacy_turmas_trilhas} mltt
     JOIN {legacy_trilhas} mlt ON mlt.codtrilha = mltt.codtrilha",
    "1=1"
);

// default sorting
$report->set_default_sort([
    'mltt.codturma' => 'ASC',
    'mltt.codtrilha' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('datainicioprematricula', 'legacy_format_date');
$report->register_formatter('criado', 'legacy_format_date');
$report->register_formatter('modificado', 'legacy_format_date');
$report->register_formatter('disponivel', 'legacy_format_bool');
$report->register_formatter('descricao', 'legacy_format_truncate_html');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
