<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Empresas de Alunos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/empresas_de_alunos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'empresas_de_alunos';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

// define which columns
$report->define_columns([
    'nome',
    'cpf',
    'userid',
    'razao_social',
    'excluido',
    'empresaprincipal',
    'datamodificacao'
]);
$report->define_headers([
    'Nome',
    'CPF',
    'ID do Usuário (Moodle)',
    'Razão Social',
    'Excluído',
    'Empresa Principal',
    'Data de Modificação'
]);

// set your exact SQL
$report->set_sql(
    "mlea.id,
    mla.nome AS nome,
    mla.cpf AS cpf,
    mla.userid AS userid,
    mle.razao_social AS razao_social,
    mlea.excluido AS excluido,
    mlea.empresaprincipal AS empresaprincipal,
    mlea.datamodificacao AS datamodificacao",
    '{legacy_empresa_aluno} mlea
       JOIN {legacy_alunos}   mla ON mlea.codaluno   = mla.codaluno
       JOIN {legacy_empresas} mle ON mle.codempresa  = mlea.codempresa',
    '1=1'
);

$report->set_default_sort([
    'mlea.codaluno' => 'DESC',
    'mle.codempresa' => 'DESC',
]);

$report->sortable(false);


$report->register_formatter('datamodificacao', 'legacy_format_date');
$report->register_formatter('excluido', 'legacy_format_bool');
$report->register_formatter('empresaprincipal', 'legacy_format_bool');
$report->register_formatter('userid', 'legacy_format_profile_url');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
