<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Corpos docentes de trilhas";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/corpos_docentes_de_trilhas.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'corpos_docentes_de_trilhas';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

// define columns and headers
$report->define_columns([
    'corpo_docente',
    'nome_turma',
    'data_inicio_turma',
    'data_fim_turma',
    'nome_trilha',
    'cargo',
]);
$report->define_headers([
    'Corpo docente',
    'Turma',
    'Início da turma',
    'Fim da turma',
    'Trilha',
    'Cargo',
]);

// apply SQL
$report->set_sql(
    "mlcdt.id,
     mlcd.nome AS corpo_docente,
     mltt.nometurma AS nome_turma,
     mlt.nome AS nome_trilha,
     mltt.datainicio AS data_inicio_turma,
     mltt.datafim AS data_fim_turma,
     mlcd.cargo",
    "{legacy_corpo_docente_trilha} mlcdt
     JOIN {legacy_corpo_docente} mlcd ON mlcd.codcorpo = mlcdt.codcorpo
     JOIN {legacy_turmas_trilhas} mltt ON mlcdt.codturma = mltt.codturma
     JOIN {legacy_trilhas} mlt ON mlt.codtrilha = mltt.codtrilha",
    "1=1"
);

// default sorting
$report->sortable(true, 'mlcdt.codcorpo, mlcdt.codturma', SORT_ASC);

$report->set_default_sort([
    'mlcdt.codcorpo' => 'ASC',
    'mlcdt.codturma' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('data_inicio_turma', 'legacy_format_date');
$report->register_formatter('data_fim_turma', 'legacy_format_date');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
