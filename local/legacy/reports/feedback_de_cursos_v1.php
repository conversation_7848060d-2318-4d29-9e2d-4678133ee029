<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Feedback de cursos (V1)";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/feedback_de_cursos_v1.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'feedback_de_cursos_v1';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_aluno',
    'nome_curso',
    'nome_turma',
    'pergunta',
    'resposta',
    'justificativa',
    'datahora',
]);

$report->define_headers([
    'Aluno',
    'Curso',
    'Turma',
    'Pergunta',
    'Resposta',
    'Justificativa',
    'Data/Hora',
]);

$report->set_sql(
    "mlfv.id,
     mla.nome AS nome_aluno,
     mlc.nome AS nome_curso,
     mltc.nometurma AS nome_turma,
     mlfv.pergunta,
     mlfv.resposta,
     mlfv.justificativa,
     mlfv.datahora",
    "{legacy_feedback_v1} mlfv
     JOIN {legacy_alunos} mla ON mla.codaluno = mlfv.codaluno
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mlfv.codcurso
     JOIN {legacy_turmas_cursos} mltc ON mltc.codturma = mlfv.codturma",
    "1=1"
);

$report->set_default_sort([
    'mlfv.codaluno' => 'ASC',
    'mlfv.codcurso' => 'ASC',
    'mlfv.codturma' => 'ASC',
    'mlfv.codperresfeedback' => 'ASC',
]);

$report->sortable(false);

// formatadores
$report->register_formatter('datahora', 'legacy_format_date');
$report->register_formatter('justificativa', 'legacy_format_truncate_html');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
