<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Situação dos Alunos em Cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/situacao_dos_alunos_em_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'situacao_dos_alunos_em_cursos';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_curso',
    'nome_turma',
    'nome_aluno',
    'datamatricula',
    'percconclusao',
    'percaproveitamento',
    'prazodeacesso',
    'datainicio',
    'datafim',
    'statusmatricula',
    'progressstatus',
    'data_de_cancelamento',
    'motivo_do_cancelamento',
    'data_do_primeiro_acesso',
    'data_do_ultimo_acesso',
    'data_de_conclusao',
]);

$report->define_headers([
    'Curso',
    'Turma',
    'Aluno',
    'Data de matrícula',
    '% Conclusão',
    '% Aproveitamento',
    'Prazo de Acesso',
    'Início',
    'Fim',
    'Status da Matrícula',
    'Status de Progresso',
    'Data de Cancelamento',
    'Motivo do Cancelamento',
    '1º Acesso',
    'Último Acesso',
    'Conclusão',
]);

$report->set_sql(
    "mlsc.codsituacaoalunocurso,
     mlc.nome AS nome_curso,
     mltc.nometurma AS nome_turma,
     mla.nome AS nome_aluno,
     mlsc.datamatricula,
     mlsc.percconclusao,
     mlsc.percaproveitamento,
     mlsc.prazodeacesso,
     mlsc.datainicio,
     mlsc.datafim,
     mlsc.statusmatricula,
     mlsc.progressstatus,
     mlsc.data_de_cancelamento,
     mlsc.motivo_do_cancelamento,
     mlsc.data_do_primeiro_acesso,
     mlsc.data_do_ultimo_acesso,
     mlsc.data_de_conclusao",
    "{legacy_situacao_curso} mlsc
     JOIN {legacy_alunos} mla ON mla.codaluno = mlsc.codaluno
     JOIN {legacy_turmas_cursos} mltc ON mltc.codturma = mlsc.codturma
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mltc.codcurso",
    "1=1"
);

$report->set_default_sort([
    'mlsc.codsituacaoalunocurso' => 'DESC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datamatricula', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('data_de_cancelamento', 'legacy_format_date');
$report->register_formatter('data_do_primeiro_acesso', 'legacy_format_date');
$report->register_formatter('data_do_ultimo_acesso', 'legacy_format_date');
$report->register_formatter('data_de_conclusao', 'legacy_format_date');
$report->register_formatter('motivo_do_cancelamento', 'legacy_format_truncate_html');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
