<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Trabalhos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/trabalhos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'trabalhos';

$report = new \local_legacy\table\persistent_table(
    $tableid,
    \local_legacy\models\trabalho::class
);

// Let the user download if requested.
$report->is_downloading($download, $tableid . '_export', $tableid);

// Omit any internal or unwanted columns.
$report->omit_columns(['id', 'timemodified', 'timecreated', 'usermodified', 'hash', 'idcurso']);

// Headers
$report->set_header_overrides([
    'codtrabalho' => 'Código do Trabalho',
    'codcurso' => 'Código do Curso',
    'codturma' => 'Código da Turma',
    'codcorpo' => 'Código do Corpo',
    'descricao' => 'Descrição',
    'dataentrega' => 'Data de Entrega',
    'permiteatraso' => 'Permite Atraso',
    'datahora' => 'Data/Hora',
    'suplemento' => 'Suplemento',
    'titulo' => 'Título',
    'tipotrabalho' => 'Tipo de Trabalho',
    'pontuacao' => 'Pontuação',
    'permitirreenvio' => 'Permite Reenvio',
    'atividadeestruturada' => 'Atividade Estruturada',
    'tipoaprovacao' => 'Tipo de Aprovação',
    'criadoem' => 'Criado em',
    'datainicio' => 'Data de Início',
]);

$report->register_formatter('descricao', 'legacy_format_truncate_html');
$report->register_formatter('suplemento', 'legacy_format_truncate_html');
$report->register_formatter('titulo', 'legacy_format_truncate_html');
$report->register_formatter('dataentrega', 'legacy_format_date');
$report->register_formatter('datahora', 'legacy_format_date');
$report->register_formatter('criadoem', 'legacy_format_date');
$report->register_formatter('datainicio', 'legacy_format_date');

$report->register_formatter('permiteatraso', 'legacy_format_bool');
$report->register_formatter('permitirreenvio', 'legacy_format_bool');
$report->register_formatter('atividadeestruturada', 'legacy_format_bool');


$report->define_baseurl($PAGE->url);

// Only output header if not exporting.
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// Render the table (25 rows per page, no initials bar).
$report->out(25, false);

// Footer for HTML view.
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
