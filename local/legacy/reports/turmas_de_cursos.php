<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Turmas de cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/turmas_de_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'turmas_de_cursos';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nometurma',
    'nome_trilha',
    'datainicio',
    'datafim',
    'datainicioprematricula',
    'datafimprematricula',
    'criado',
    'modificado',
    'disponivel',
    'cancelada',
    'cargahoraria',
    'descricao',
]);

$report->define_headers([
    'Turma',
    'Curso',
    'Início',
    'Fim',
    'Início Pré-Matrícula',
    'Fim Pré-Matrícula',
    'Criado em',
    'Modificado em',
    'Disponível',
    'Cancelada',
    'Carga Horária',
    'Descrição',
]);

// apply SQL
$report->set_sql(
    "mltc.id,
     mltc.nometurma,
     mltc.datainicio,
     mltc.datafim,
     mltc.criado,
     mltc.modificado,
     mltc.disponivel,
     mltc.descricao,
     mltc.cargahoraria,
     mltc.datainicioprematricula,
     mltc.datafimprematricula,
     mltc.cancelada,
     mlc.nome AS nome_trilha",
    "{legacy_turmas_cursos} mltc
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mltc.codcurso",
    "1=1"
);

// default sorting
$report->set_default_sort([
    'mltc.codturma' => 'ASC',
    'mltc.codcurso' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('datainicioprematricula', 'legacy_format_date');
$report->register_formatter('datafimprematricula', 'legacy_format_date');
$report->register_formatter('criado', 'legacy_format_date');
$report->register_formatter('modificado', 'legacy_format_date');
$report->register_formatter('disponivel', 'legacy_format_bool');
$report->register_formatter('cancelada', 'legacy_format_bool');
$report->register_formatter('descricao', 'legacy_format_truncate_html');
$report->register_formatter('cargahoraria', 'legacy_format_duration_hours');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
