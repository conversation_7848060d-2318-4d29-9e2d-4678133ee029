<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Corpos docentes de cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/corpos_docentes_de_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'corpos_docentes_de_cursos';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

// define columns and headers
$report->define_columns([
    'corpo_docente',
    'nome_turma',
    'data_inicio_turma',
    'data_fim_turma',
    'nome_curso',
    'idcurso',
    'dataalocacao',
    'cargo',
]);
$report->define_headers([
    'Corpo docente',
    'Cargo',
    'Turma',
    'Início da turma',
    'Fim da turma',
    'Curso',
    'ID do curso',
    'Data de alocação',
]);

// apply SQL
$report->set_sql(
    "mlcdc.id,
     mlcd.nome AS corpo_docente,
     mltc.nometurma AS nome_turma,
     mlc.nome AS nome_curso,
     mlcdc.dataalocacao,
     mlc.idcurso,
     mltc.datainicio AS data_inicio_turma,
     mltc.datafim AS data_fim_turma,
     mlcd.cargo",
    "{legacy_corpo_docente_curso} mlcdc
     JOIN {legacy_corpo_docente} mlcd ON mlcd.codcorpo = mlcdc.codcorpo
     JOIN {legacy_turmas_cursos} mltc ON mlcdc.codturma = mltc.codturma
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mltc.codcurso",
    "1=1"
);

// default sorting
$report->set_default_sort([
    'mlcdc.codcorpo' => 'ASC',
    'mlcdc.corturma' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('dataalocacao', 'legacy_format_date');
$report->register_formatter('data_inicio_turma', 'legacy_format_date');
$report->register_formatter('data_fim_turma', 'legacy_format_date');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
