<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$title = "Turmas de cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/turmas_de_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'turmas_de_cursos';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_aluno',
    'nome_turma',
    'nome_curso',
    'id_curso',
    'titulo',
    'tipotrabalho',
    'tipoaprovacao',
    'arquivo',
    'status',
    'entregue',
    'pontos',
    'datafinal',
    'datadivulgacaonotas',
    'observacoes',
    'consideracoes',
    'corpo_docente',
]);

$report->define_headers([
    'Aluno',
    'Turma',
    'Curso',
    'ID Curso',
    'Título',
    'Tipo de Trabalho',
    'Tipo de Aprovação',
    'Corpo Docente',
    'Arquivo',
    'Observações',
    'Status',
    'Entregue',
    'Pontos',
    'Data Final',
    'Data da Nota',
    'Considerações',
]);

$report->set_sql(
    "mlta.id,
     mlt.titulo,
     mlt.tipotrabalho,
     mlt.tipoaprovacao,
     mlc.idcurso AS id_curso,
     mlc.nome AS nome_curso,
     mltc.nometurma AS nome_turma,
     mla.nome AS nome_aluno,
     mlta.arquivo,
     mlta.observacoes,
     mlta.status,
     mlta.entregue,
     mlta.datafinal,
     mlta.pontos,
     mlta.datadivulgacaonotas,
     mlcd.nome AS corpo_docente,
     mlta.consideracoes",
    "{legacy_trabalhos_alunos} mlta
     JOIN {legacy_trabalhos} mlt ON mlt.codtrabalho = mlta.codtrabalho
     JOIN {legacy_alunos} mla ON mlta.codaluno = mla.codaluno
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mlt.codcurso
     JOIN {legacy_turmas_cursos} mltc ON mltc.codturma = mlt.codturma
     JOIN {legacy_corpo_docente} mlcd ON mlcd.codcorpo = mlt.codcorpo",
    "1=1"
);

$report->set_default_sort([
    'mlta.codaluno' => 'ASC',
    'mlta.codtrabalho' => 'ASC',
]);

$report->sortable(false);

// formatadores
$report->register_formatter('datafinal', 'legacy_format_date');
$report->register_formatter('datadivulgacaonotas', 'legacy_format_date');
$report->register_formatter('entregue', 'legacy_format_bool');
$report->register_formatter('observacoes', 'legacy_format_truncate_html');
$report->register_formatter('consideracoes', 'legacy_format_truncate_html');

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
