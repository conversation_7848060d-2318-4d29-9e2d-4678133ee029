<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = 'Itens de trilhas realizados';
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/itens_de_trilhas_realizados.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'topicos_realizados';

// instantiate the custom table
$report = new custom_sql_table($tableid);

// allow download/export
$report->is_downloading($download, $tableid . '_export', $tableid);

// define columns and headers
$report->define_columns([
    'nome_trilha',
    'nome_item',
    'nome_turma',
    'nome_aluno',
    'porcentagem_conclusao',
    'nota',
]);
$report->define_headers([
    'Trilha',
    'Item',
    'Turma',
    'Aluno',
    'Porcentagem de conclusão',
    'Nota',
]);

$fields = "
    mlita.id,
	mlt.nome AS nome_trilha,
	mlit.name AS nome_item,
	mltt.nometurma AS nome_turma,
	mla.nome as nome_aluno,
	mlita.porcentagem_conclusao,
	mlita.nota";
$from = "
    {legacy_itens_trilhas_alunos} mlita 
	JOIN {legacy_itens_trilhas} mlit ON (mlit.coditem = mlita.coditem)
	JOIN {legacy_alunos} mla ON (mla.codaluno = mlita.codaluno)
	JOIN {legacy_turmas_trilhas} mltt ON (mltt.codturma = mlita.codturma)
	JOIN {legacy_trilhas} mlt ON (mlt.codtrilha = mlita.codtrilha)";
$where = '1=1';

$report->set_sql($fields, $from, $where);

// default sorting
$report->set_default_sort([
    'mlita.codtrilha' => 'ASC',
    'mlita.codturma' => 'ASC',
    'mlita.codaluno' => 'ASC',
    'mlita.coditem' => 'ASC',
]);

// set base URL so paging/sort/export links keep parameters
$report->define_baseurl($PAGE->url);

// render header if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

// output the table: 25 rows per page, no initials bar
$report->out(25, false);

// render footer if not exporting
if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
