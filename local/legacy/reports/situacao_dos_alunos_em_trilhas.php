<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Situação dos Alunos em Cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/situacao_dos_alunos_em_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'situacao_dos_alunos_em_cursos';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_trilha',
    'nome_turma',
    'nome_aluno',
    'percconclusao',
    'percaproveitamento',
    'prazodeacesso',
    'datainicio',
    'datafim',
    'statusmatricula',
    'progressstatus',
    'data_do_primeiro_acesso',
    'data_do_ultimo_acesso',
    'data_de_conclusao',
]);

$report->define_headers([
    'Trilha',
    'Turma',
    'Aluno',
    '% Conclusão',
    '% Aproveitamento',
    'Prazo de Acesso',
    'Início',
    'Fim',
    'Status da Matrícula',
    'Status de Progresso',
    'Primeiro Acesso',
    'Último Acesso',
    'Conclusão',
]);

$report->set_sql(
    "mlst.codsituacao,
     mlt.nome AS nome_trilha,
     mltt.nometurma AS nome_turma,
     mla.nome AS nome_aluno,
     mlst.percconclusao,
     mlst.percaproveitamento,
     mlst.prazodeacesso,
     mlst.datainicio,
     mlst.datafim,
     mlst.statusmatricula,
     mlst.progressstatus,
     mlst.data_do_primeiro_acesso,
     mlst.data_do_ultimo_acesso,
     mlst.data_de_conclusao",
    "{legacy_situacao_trilha} mlst
     JOIN {legacy_alunos} mla ON mla.codaluno = mlst.codaluno
     JOIN {legacy_turmas_trilhas} mltt ON mltt.codturma = mlst.codturma
     JOIN {legacy_trilhas} mlt ON mlt.codtrilha = mlst.codtrilha",
    "1=1"
);

$report->set_default_sort([
    'mlst.codsituacao' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('data_do_primeiro_acesso', 'legacy_format_date');
$report->register_formatter('data_do_ultimo_acesso', 'legacy_format_date');
$report->register_formatter('data_de_conclusao', 'legacy_format_date');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
