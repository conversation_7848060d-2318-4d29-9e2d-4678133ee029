<?php

require_once(__DIR__ . '/../../../config.php');
require_once(__DIR__ . '/../lib.php');

use local_legacy\table\custom_sql_table;

$context = context_system::instance();
require_capability('local/legacy:viewreports', $context);

$title = "Segmentos de Turmas de Cursos";
$PAGE->set_context($context);
$PAGE->set_url(new moodle_url('/local/legacy/reports/segmentos_de_turmas_de_cursos.php'));
$PAGE->set_title($title);
$PAGE->set_heading($title);

$download = optional_param('download', '', PARAM_ALPHA);
$tableid  = 'segmentos_de_turmas_de_cursos';

$report = new custom_sql_table($tableid);
$report->is_downloading($download, $tableid . "_export", $tableid);

$report->define_columns([
    'nome_segmento',
    'nome_turma',
    'nome_curso',
    'datainicio',
    'datafim',
    'descricao',
    'cancelada',
]);

$report->define_headers([
    'Segmento',
    'Turma',
    'Início',
    'Fim',
    'Descrição',
    'Cancelada',
    'Curso',
]);

$report->set_sql(
    "mlstc.id,
    mltc.nometurma AS nome_turma,
     mls.nomesegmento AS nome_segmento,
     mlc.nome AS nome_curso,
     mltc.datainicio,
     mltc.datafim,
     mltc.descricao,
     mltc.cancelada",
    "{legacy_segmento_turma_curso} mlstc
     JOIN {legacy_turmas_cursos} mltc ON mltc.codturma = mlstc.codturma
     JOIN {legacy_segmentos} mls ON mls.codsegmento = mlstc.codsegmento
     JOIN {legacy_cursos} mlc ON mlc.codcurso = mltc.codcurso",
    "1=1"
);

$report->set_default_sort([
    'mlstc.codsegmento' => 'ASC',
    'mlstc.codturma' => 'ASC',
]);

$report->sortable(false);

// formatters
$report->register_formatter('datainicio', 'legacy_format_date');
$report->register_formatter('datafim', 'legacy_format_date');
$report->register_formatter('descricao', 'legacy_format_truncate_html');
$report->register_formatter('cancelada', 'legacy_format_bool');

$report->define_baseurl($PAGE->url);

if (!$report->is_downloading()) {
    echo $OUTPUT->header();
    echo $OUTPUT->heading($title);
}

$report->out(25, false);

if (!$report->is_downloading()) {
    echo $OUTPUT->footer();
}
