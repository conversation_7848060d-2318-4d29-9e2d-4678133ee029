<?php

use local_legacy\certificate\certificate_renderer;
use local_legacy\models\certificate\certificate_issue;

require(__DIR__ . '/../../../config.php');

require_login();

$code = optional_param('code', '', PARAM_TEXT);
$issue = certificate_issue::get_by_code($code);

if(!$issue){
    header("HTTP/1.0 404 Not Found");
    die();
}

$PAGE->set_url(new moodle_url('/local/legacy/certificates/download.php', []));
$PAGE->set_context(context_system::instance());

$renderer = new certificate_renderer($issue);
$pdf = $renderer->render();

$pdf->Output("$code.pdf", 'I');
// $pdf->Output("$code.pdf", 'D');
