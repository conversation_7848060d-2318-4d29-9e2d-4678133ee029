<?php

use local_legacy\certificate\certificate_renderer;
use local_legacy\models\certificate\certificate_issue;

require(__DIR__ . '/../../../config.php');

$code = optional_param('AT', optional_param('code', null, PARAM_TEXT), PARAM_TEXT);

$url = new moodle_url('/local/legacy/certificates/verify.php');
$PAGE->set_url($url);
$PAGE->set_context(context_system::instance());
$PAGE->set_heading(get_string('certificateverification', 'local_legacy'));
$PAGE->set_pagelayout('base');

echo $OUTPUT->header();

$data = [
    'heading' => get_string('certificateverification', 'local_legacy'),
];

if (!$code) {
    $data += [
        'showform' => true,
        'formlabel' => get_string('entercode', 'local_legacy'),
        'placeholder' => get_string('certificatecode', 'local_legacy'),
        'buttontext' => get_string('verify', 'local_legacy'),
    ];
} else {
    $issue = certificate_issue::get_by_code($code);

    if ($issue) {
        $renderer = new certificate_renderer($issue);
        $rendered_text = $renderer->get_processed_cert_text();

        $data += [
            'message' => get_string('certificatevalid', 'local_legacy'),
            'alertclass' => 'alert-success',
            'certificate' => format_text($rendered_text, FORMAT_HTML),
        ];
    } else {
        $data += [
            'message' => get_string('invalidcertificate', 'local_legacy'),
            'alertclass' => 'alert-danger',
        ];
    }
}

echo $OUTPUT->render_from_template('local_legacy/verify_certificate', $data);

echo $OUTPUT->footer();
