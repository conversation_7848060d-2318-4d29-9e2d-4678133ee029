<?php
// local/legacy/certificates/edit.php

require_once(__DIR__ . '/../../../config.php');

use local_legacy\models\certificate\certificate;
use local_legacy\certificate\forms\edit_form;

global $PAGE, $OUTPUT, $USER;

// Ensure user is logged in and set up page context
require_login();
$systemcontext = context_system::instance();
$PAGE->set_context($systemcontext);
$PAGE->set_url(new moodle_url('/local/legacy/certificates/edit.php'));
$PAGE->set_title(get_string('certificate:editcertificate', 'local_legacy'));
$PAGE->set_heading(get_string('certificate:editcertificate', 'local_legacy'));

$certid      = optional_param('id', 0, PARAM_INT);
$certificate = null;
$formdata    = new stdClass();

if ($certid) {
    // Load existing certificate or throw if invalid
    $certificate = certificate::get_by_id($certid);
    if (!$certificate) {
        throw new moodle_exception('certificate:invalidcertificateid', 'local_legacy');
    }
    // Pre-fill form data using our to_formdata() helper
    $formdata = $certificate->to_formdata();
}

// Instantiate the form, passing any existing data
$mform = new edit_form();
if (!empty((array)$formdata)) {
    $mform->set_data($formdata);
}

// Handle form cancellation
if ($mform->is_cancelled()) {
    redirect(new moodle_url('/local/legacy/certificates/index.php'));
}

// Handle form submission
if ($data = $mform->get_data()) {
    if (!empty($data->id)) {
        $certificate = certificate::get_by_id($data->id);
        if (!$certificate) {
            throw new moodle_exception('certificate:invalidcertificateid', 'local_legacy');
        }
    } else {
        $certificate = new certificate(0);
    }

    // Updating or creating certificate
    $certificate->update_from_formdata($data)->save();

    if (!empty($data->certificateimage)) {
        $fileinfo = certificate::get_certificate_image_fileinfo($certificate->get('id'));
        file_save_draft_area_files(
            $data->certificateimage,
            $fileinfo['contextid'],
            $fileinfo['component'],
            $fileinfo['filearea'],
            $fileinfo['itemid']
        );
    }

    if (!empty($data->secondimage)) {
        $fileinfo2 = certificate::get_certificate_secondimage_fileinfo($certificate->get('id'));
        file_save_draft_area_files(
            $data->secondimage,
            $fileinfo2['contextid'],
            $fileinfo2['component'],
            $fileinfo2['filearea'],
            $fileinfo2['itemid']
        );
    }

    redirect(
        new moodle_url('/local/legacy/certificates/list.php'),
        get_string('certificate:certificatesaved', 'local_legacy')
    );
}

// Display the form
echo $OUTPUT->header();
if ($certid) {
    echo $OUTPUT->heading(
        get_string('certificate:editexistingcertificate', 'local_legacy', $certificate->get('name'))
    );
} else {
    echo $OUTPUT->heading(get_string('certificate:createanewcertificate', 'local_legacy'));
}
$mform->display();
echo $OUTPUT->footer();
