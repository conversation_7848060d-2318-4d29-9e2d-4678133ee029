<?php

namespace local_legacy\certificate;

require_once($CFG->libdir . '/pdflib.php');

use local_legacy\models\certificate\certificate;
use local_legacy\models\interfaces\certificate_issue_interface;
use pdf;

class certificate_renderer {

    const QRCODE_SIZE = 50;

    protected certificate_issue_interface $issue;
    /** @var object|null Cached tags data after first generation */
    protected ?object $tagsdata = null;
    
    public function __construct(certificate_issue_interface $issue) {
        $this->issue = $issue;
    }

    /**
     * Generates and returns an object containing all tag replacements.
     * This method is called only once; subsequent calls return the cached data.
     *
     * @return object
     */
    public function get_tags_data(): object {
        if ($this->tagsdata === null) {
            $this->tagsdata = $this->fetch_tags_data();
        }
        return $this->tagsdata;
    }

    /**
     * Collects raw tag values from the issue and returns them as an object.
     * Intended for internal use; called by get_tags_data().
     *
     * @return object
     */
    protected function fetch_tags_data(): object {
        $situacao = $this->issue->get_situacao_matricula();
        $solucao  = $situacao->get_solucao_educacional();
        $aluno    = $situacao->get_aluno();

        return (object)[
            'CARGA_HORARIA'             => $solucao?->get_formatted_carga_horaria(),
            'DATA_EXTENSO'              => $this->get_formatted_current_date(),
            'LOGIN'                     => $aluno?->get_formatted_login(),
            'NOME_ALUNO'                => $aluno?->get_formatted_nome(),
            'NOME_CURSO_OU_PROGRAMA'    => $solucao->get_formatted_nome(),
            'PERCENTUAL_CONCLUSAO'      => $situacao->get_formatted_percentual_de_conclusao(),
            'PERIODO'                   => null,
            'TERMO_CURSO_OU_PROGRAMA'   => null,
            'TERMO_TIPO_CURSO_OU_PROGRAMA' => null,
            'CARGA_HORARIA_TRILHA'      => $solucao?->get_formatted_carga_horaria(),
            'CPF_DOCINTERNACIONAL'      => $aluno?->get_formatted_cpf_docinternacional(),
            'FORMATO_SOLUCAO'           => null,
            'NOME_ITEM'                 => null,
            'PERCENTUAL_APROVEITAMENTO' => $situacao->get_formatted_percentual_de_aproveitamento(),
            'TERMO_ITEM'                => null,
            'TERMO_PERIODO'             => null,
            'TERMO_TIPO_ITEM'           => null,
            'UF'                        => $aluno?->get_formatted_uf(),
            'CONTEUDOPROGRAMATICO'      => $solucao->get_formatted_conteudo_programatico(),
            'EMPRESA'                   => null,
            'APROVEITAMENTO_EXTENSO'    => null,
        ];
    }

    /**
     * Formats the current date in Portuguese style without leading zero.
     *
     * @return string
     */
    public function get_formatted_current_date(): string {
        $date = userdate(time(), '%d de %B de %Y');
        return ltrim($date, '0');
    }

    /**
     * Returns the certificate HTML text (first page) with all tags replaced exactly once.
     *
     * @return string
     */
    public function get_processed_cert_text(): string {
        $cert    = $this->issue->get_certificate();
        $rawHtml = $cert->get('certificatetext'); // HTML with [TAG] placeholders
        $data    = $this->get_tags_data();

        // Replace each [TAG] with its corresponding value
        foreach ((array)$data as $tag => $value) {
            $rawHtml = str_replace('[' . strtoupper($tag) . ']', $value ?? $tag, $rawHtml);
        }
        // Remove any leftover placeholders
        return preg_replace('/\[[A-Z0-9_]+\]/', '', $rawHtml);
    }

    /**
     * Returns the second-page HTML text with all tags replaced exactly once.
     *
     * @return string
     */
    public function get_processed_second_page_text(): string {
        $cert     = $this->issue->get_certificate();
        $rawHtml2 = $cert->get('secondpagetext') ?? ''; // HTML with [TAG] placeholders for second page
        $data     = $this->get_tags_data();

        // Replace each [TAG] with its corresponding value
        foreach ((array)$data as $tag => $value) {
            $rawHtml2 = str_replace('[' . strtoupper($tag) . ']', $value, $rawHtml2);
        }
        // Remove any leftover placeholders
        return preg_replace('/\[[A-Z0-9_]+\]/', '', $rawHtml2);
    }

    /**
     * Creates and returns a configured PDF object (TCPDF/Moodle wrapper).
     *
     * @return pdf
     */
    protected function create_pdf_object(): pdf {
        $cert = $this->issue->get_certificate();

        $orientation = 'L';
        if ((int)$cert->get('height') > (int)$cert->get('width')) {
            $orientation = 'P';
        }

        $pdf = new pdf(
            $orientation,
            'mm',
            [(int)$cert->get('width'), (int)$cert->get('height')],
            true,
            'UTF-8'
        );
        $pdf->SetTitle(format_string($cert->get('name'), true));
        $pdf->SetSubject(format_string($cert->get('name'), true));

        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        $pdf->SetAutoPageBreak(false, 0);
        $pdf->setFontSubsetting(true);
        $pdf->SetMargins(0, 0, 0, true);

        return $pdf;
    }

    /**
     * Draws a QR code on the PDF, pointing to the verification URL.
     *
     * @param pdf    $pdf  TCPDF object
     * @param string $code Validation code to generate URL
     */
    protected function print_qrcode(pdf $pdf, string $code) {
        global $CFG;
        $cert  = $this->issue->get_certificate();
        $codex = (int)$cert->get('codex');
        $codey = (int)$cert->get('codey');

        if($codex < 0){
            $codex = (int)$cert->get('width') + $codex - static::QRCODE_SIZE;
        }

        if($codey < 0){
            $codey = (int)$cert->get('height') + $codey - static::QRCODE_SIZE;
        }

        $style = [
            'border'        => 2,
            'vpadding'      => 'auto',
            'hpadding'      => 'auto',
            'fgcolor'       => [0, 0, 0],
            'bgcolor'       => [255, 255, 255],
            'module_width'  => 1,
            'module_height' => 1
        ];

        // $codeurl = new \moodle_url($CFG->wwwroot . '/local/legacy/certificates/verify.php');
        $codeurl = new \moodle_url($CFG->wwwroot);

        $codeurl->param('AT', $code);

        $pdf->write2DBarcode(
            $codeurl->out(false),
            'QRCODE,M',
            $codex,
            $codey,
            static::QRCODE_SIZE, static::QRCODE_SIZE,
            $style,
            'N'
        );
        $pdf->SetXY($codex, $codey + (static::QRCODE_SIZE-1));
        $pdf->SetFillColor(255, 255, 255);
        $pdf->Cell(static::QRCODE_SIZE, 0, $code, 'LRB', 0, 'C', true, '', 2);
    }

    /**
     * Renders only the first page: background image, processed HTML text, and QR code if needed.
     *
     * @param pdf $pdf  TCPDF object already created
     * @throws \moodle_exception if background image is missing
     */
    protected function render_first_page(pdf $pdf) {
        global $CFG;
        $cert = $this->issue->get_certificate();
        $fs   = get_file_storage();

        $page_width = (int)$cert->get('width');
        $page_height = (int)$cert->get('height');

        $pdf->AddPage();

        $text_x = (int)$cert->get('certificatetextx');
        $text_y = (int)$cert->get('certificatetexty');

        $pdf->SetMargins(0, 0, 0, true); // Fixing text centralization
        $pdf->SetXY(0, $text_y);

        $bgfile = $this->issue->get_certificate()->get_first_page_image();
        if ($bgfile) {
            $tmpfname = $bgfile->copy_content_to_temp(
                certificate::CERTIFICATE_COMPONENT_NAME,
                'first_image_'
            );
            $pdf->Image($tmpfname, 0, 0, $page_width, $page_height);
            @unlink($tmpfname);
        }

        $html = $this->get_processed_cert_text();

        $max_width = $page_width - 2 * $text_x;
        $pdf->writeHTMLCell(
            $max_width, 0, $text_x, '', $html, 0, 0, 0, true, 'C'
        );

        if ($cert->get('printqrcode') && $cert->get('qrcodefirstpage')) {
            $this->print_qrcode($pdf, (string)$this->issue->get('code'));
        }
    }

    /**
     * Renders only the second page: background image, processed second-page HTML text, and leaves room for QR code if needed.
     *
     * @param pdf $pdf  TCPDF object
     * @throws \moodle_exception if second-page image is missing
     */
    // protected function render_second_page(pdf $pdf) {
    //     $cert = $this->issue->get_certificate();
    //     $fs   = get_file_storage();

    //     // Add a new page for second-page content
    //     $pdf->AddPage();

    //     // Draw second-page background image if provided
    //     $bgfile2 = $this->issue->get_certificate()->get_second_page_image();
    //     if ($bgfile2) {
    //         $tmp2 = $bgfile2->copy_content_to_temp(
    //             certificate::CERTIFICATE_COMPONENT_NAME,
    //             'second_image_'
    //         );
    //         $pdf->Image(
    //             $tmp2,
    //             0, 0,
    //             (int)$cert->get('width'),
    //             (int)$cert->get('height')
    //         );
    //         @unlink($tmp2);
    //     }

    //     // Retrieve the processed second-page HTML
    //     $html2 = $this->get_processed_second_page_text();

    //     // Position and write the second-page HTML content
    //     if ($html2 !== '') {
    //         $pdf->SetXY((int)$cert->get('secondpagex'), (int)$cert->get('secondpagey'));
    //         $pdf->writeHTMLCell(
    //             0, 0,
    //             '',
    //             '',
    //             $html2,
    //             0, 0, 0, true, 'C'
    //         );
    //     }
    // }
    protected function render_second_page(pdf $pdf) {
        $cert = $this->issue->get_certificate();
        $fs   = get_file_storage();

        $page_width = (int)$cert->get('width');
        $page_height = (int)$cert->get('height');

        $pdf->AddPage();

        $text_x = (int)$cert->get('secondpagex');
        $text_y = (int)$cert->get('secondpagey');

        $pdf->SetMargins(0, 0, 0, true); // Fixing text centralization
        $pdf->SetXY(0, $text_y);

        $bgfile2 = $this->issue->get_certificate()->get_second_page_image();
        if ($bgfile2) {
            $tmp2 = $bgfile2->copy_content_to_temp(
                certificate::CERTIFICATE_COMPONENT_NAME,
                'second_image_'
            );
            $pdf->Image($tmp2, 0, 0, $page_width, $page_height);
            @unlink($tmp2);
        }

        $html2 = $this->get_processed_second_page_text();

        $max_width = $page_width - 2 * $text_x;
        $pdf->writeHTMLCell(
            $max_width, 0, $text_x, '', $html2, 0, 0, 0, true, 'C'
        );
    }


    /**
     * Creates and returns the PDF with all pages, tags applied once, and QR code(s).
     *
     * @return pdf
     * @throws \moodle_exception if any image file is missing
     */
    public function render(): pdf {
        $cert = $this->issue->get_certificate();

        // Instantiate PDF
        $pdf = $this->create_pdf_object();

        // Render the first page using the dedicated method
        $this->render_first_page($pdf);

        // Render the second page if enabled
        if ($cert->get('enablesecondpage')) {
            $this->render_second_page($pdf);
        }

        // If QR code is not on the first page, print it on second or new page
        if ($cert->get('printqrcode') && !$cert->get('qrcodefirstpage')) {
            if (!$cert->get('enablesecondpage')) {
                $pdf->AddPage();
            }
            $this->print_qrcode($pdf, (string)$this->issue->get('code'));
        }

        return $pdf;
    }
}
