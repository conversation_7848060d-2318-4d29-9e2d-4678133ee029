<?php

namespace local_legacy\certificate\forms;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');
require_once($CFG->libdir . '/filelib.php');

use context_system;
use local_legacy\models\certificate\certificate;
use moodleform;

class edit_form extends moodleform {

    public function definition() {
        $mform =& $this->_form;

        // Hidden fields
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        $mform->addElement('hidden', 'solutiontype');
        $mform->setType('solutiontype', PARAM_TEXT);

        $mform->addElement('hidden', 'solutionid');
        $mform->setType('solutionid', PARAM_INT);

        $mform->addElement('hidden', 'legacyid');
        $mform->setType('legacyid', PARAM_INT);

        // === Section: General ===
        $mform->addElement('header', 'general', get_string('certificate:general', 'local_legacy'));

        // Certificate name
        $mform->addElement(
            'text',
            'name',
            get_string('certificate:certificatename', 'local_legacy'),
            ['size' => '64']
        );
        $mform->setType('name', PARAM_TEXT);
        $mform->addRule(
            'name',
            get_string('certificate:err_required', 'local_legacy'),
            'required',
            null,
            'client'
        );
        $mform->addHelpButton('name', 'certificate:certificatename', 'local_legacy');

        // === Section: Design Options ===
        $mform->addElement('header', 'designoptions', get_string('certificate:designoptions', 'local_legacy'));

        // First‐page background image
        $mform->addElement(
            'filemanager',
            'certificateimage',
            get_string('certificate:certificateimage', 'local_legacy'),
            null,
            $this->get_filemanager_options_array()
        );
        $mform->addHelpButton('certificateimage', 'certificate:certificateimage', 'local_legacy');

        // Main certificate text (editor)
        $mform->addElement(
            'editor',
            'certificatetext',
            get_string('certificate:certificatetext', 'local_legacy'),
            null,
            $this->get_editor_options()
        );
        $mform->addRule(
            'certificatetext',
            get_string('certificate:err_required', 'local_legacy'),
            'required',
            null,
            'client'
        );
        $mform->addHelpButton('certificatetext', 'certificate:certificatetext', 'local_legacy');

        // Page Width (mm)
        $mform->addElement('text', 'width', get_string('certificate:width', 'local_legacy'), ['size' => '5']);
        $mform->setType('width', PARAM_INT);
        $mform->setDefault('width', 297);
        $mform->addHelpButton('width', 'certificate:width', 'local_legacy');

        // Page Height (mm)
        $mform->addElement('text', 'height', get_string('certificate:height', 'local_legacy'), ['size' => '5']);
        $mform->setType('height', PARAM_INT);
        $mform->setDefault('height', 210);
        $mform->addHelpButton('height', 'certificate:height', 'local_legacy');

        // Text Position X (mm)
        $mform->addElement(
            'text',
            'certificatetextx',
            get_string('certificate:certificatetextx', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('certificatetextx', PARAM_INT);
        $mform->setDefault('certificatetextx', 10);
        $mform->addHelpButton('certificatetextx', 'certificate:certificatetextx', 'local_legacy');

        // Text Position Y (mm)
        $mform->addElement(
            'text',
            'certificatetexty',
            get_string('certificate:certificatetexty', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('certificatetexty', PARAM_INT);
        $mform->setDefault('certificatetexty', 50);
        $mform->addHelpButton('certificatetexty', 'certificate:certificatetexty', 'local_legacy');

        // === Section: Second Page Options ===
        $mform->addElement('header', 'secondpageoptions', get_string('certificate:secondpageoptions', 'local_legacy'));

        // Enable second page?
        $mform->addElement(
            'selectyesno',
            'enablesecondpage',
            get_string('certificate:enablesecondpage', 'local_legacy')
        );
        $mform->setDefault('enablesecondpage', 0);
        $mform->addHelpButton('enablesecondpage', 'certificate:enablesecondpage', 'local_legacy');

        // Second‐page background image
        $mform->addElement(
            'filemanager',
            'secondimage',
            get_string('certificate:secondimage', 'local_legacy'),
            null,
            $this->get_filemanager_options_array()
        );
        $mform->addHelpButton('secondimage', 'certificate:secondimage', 'local_legacy');
        $mform->disabledIf('secondimage', 'enablesecondpage', 'eq', 0);

        // Second‐page text (editor)
        $mform->addElement(
            'editor',
            'secondpagetext',
            get_string('certificate:secondpagetext', 'local_legacy'),
            null,
            $this->get_editor_options()
        );
        $mform->addHelpButton('secondpagetext', 'certificate:secondpagetext', 'local_legacy');
        $mform->disabledIf('secondpagetext', 'enablesecondpage', 'eq', 0);

        // Text Position X (second page)
        $mform->addElement(
            'text',
            'secondpagex',
            get_string('certificate:secondpagex', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('secondpagex', PARAM_INT);
        $mform->setDefault('secondpagex', 10);
        $mform->addHelpButton('secondpagex', 'certificate:secondpagex', 'local_legacy');
        $mform->disabledIf('secondpagex', 'enablesecondpage', 'eq', 0);

        // Text Position Y (second page)
        $mform->addElement(
            'text',
            'secondpagey',
            get_string('certificate:secondpagey', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('secondpagey', PARAM_INT);
        $mform->setDefault('secondpagey', 50);
        $mform->addHelpButton('secondpagey', 'certificate:secondpagey', 'local_legacy');
        $mform->disabledIf('secondpagey', 'enablesecondpage', 'eq', 0);

        // === Section: QR Code Options ===
        $mform->addElement('header', 'qrcodeoptions', get_string('certificate:qrcodeoptions', 'local_legacy'));

        // Print QR code?
        $mform->addElement(
            'selectyesno',
            'printqrcode',
            get_string('certificate:printqrcode', 'local_legacy')
        );
        $mform->setDefault('printqrcode', 1);
        $mform->addHelpButton('printqrcode', 'certificate:printqrcode', 'local_legacy');

        // QR Code Position X
        $mform->addElement(
            'text',
            'codex',
            get_string('certificate:codex', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('codex', PARAM_INT);
        $mform->setDefault('codex', 10);
        $mform->addHelpButton('codex', 'certificate:codex', 'local_legacy');
        $mform->disabledIf('codex', 'printqrcode', 'eq', 0);

        // QR Code Position Y
        $mform->addElement(
            'text',
            'codey',
            get_string('certificate:codey', 'local_legacy'),
            ['size' => '5']
        );
        $mform->setType('codey', PARAM_INT);
        $mform->setDefault('codey', 10);
        $mform->addHelpButton('codey', 'certificate:codey', 'local_legacy');
        $mform->disabledIf('codey', 'printqrcode', 'eq', 0);

        // Print QR Code on first page?
        $mform->addElement(
            'selectyesno',
            'qrcodefirstpage',
            get_string('certificate:qrcodefirstpage', 'local_legacy')
        );
        $mform->setDefault('qrcodefirstpage', 0);
        $mform->addHelpButton('qrcodefirstpage', 'certificate:qrcodefirstpage', 'local_legacy');
        $mform->disabledIf('qrcodefirstpage', 'printqrcode', 'eq', 0);

        // Submit buttons
        $this->add_action_buttons(false, get_string('certificate:savechanges', 'local_legacy'));
    }

    /**
     * validation: ensure numeric fields are ≥ 0.
     *
     * @param array $data Submitted form data
     * @param array $files Uploaded files
     * @return array $errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        if (isset($data['width']) && $data['width'] <= 0) {
            $errors['width'] = get_string('certificate:error_invalidwidth', 'local_legacy');
        }
        if (isset($data['height']) && $data['height'] <= 0) {
            $errors['height'] = get_string('certificate:error_invalidheight', 'local_legacy');
        }
        if (isset($data['certificatetextx']) && $data['certificatetextx'] < 0) {
            $errors['certificatetextx'] = get_string('certificate:error_invalidposition', 'local_legacy');
        }
        if (isset($data['certificatetexty']) && $data['certificatetexty'] < 0) {
            $errors['certificatetexty'] = get_string('certificate:error_invalidposition', 'local_legacy');
        }
        if (!empty($data['enablesecondpage'])) {
            if (isset($data['secondpagex']) && $data['secondpagex'] < 0) {
                $errors['secondpagex'] = get_string('certificate:error_invalidposition', 'local_legacy');
            }
            if (isset($data['secondpagey']) && $data['secondpagey'] < 0) {
                $errors['secondpagey'] = get_string('certificate:error_invalidposition', 'local_legacy');
            }
        }
        if (!empty($data['printqrcode'])) {
            if (isset($data['codex']) && $data['codex'] < 0) {
                $errors['codex'] = get_string('certificate:error_invalidposition', 'local_legacy');
            }
            if (isset($data['codey']) && $data['codey'] < 0) {
                $errors['codey'] = get_string('certificate:error_invalidposition', 'local_legacy');
            }
        }

        return $errors;
    }

    /**
     * Options for filemanager (image upload).
     *
     * @return array
     */
    private function get_filemanager_options_array(): array {
        return [
            'subdirs'        => 0,
            'maxbytes'       => 0,
            'maxfiles'       => 1,
            'accepted_types' => ['image']
        ];
    }

    /**
     * Options for HTML editor fields.
     *
     * @return array
     */
    private function get_editor_options(): array {
        return [
            'maxfiles' => EDITOR_UNLIMITED_FILES,
            'maxbytes' => 0,
            'trusttext'=> false,
            'context'  => context_system::instance()
        ];
    }
}
