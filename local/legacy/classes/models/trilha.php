<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;
use local_legacy\models\interfaces\solucao_educational_interface;

defined('MOODLE_INTERNAL') || die();

class trilha extends abstract_legacy_entity implements solucao_educational_interface {
    const TABLE = 'legacy_trilhas';

    public static function define_identifier(): string {
        return 'codtrilha';
    }

    protected static function define_model_properties(): array {
        return [
            'courseid' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codtrilha' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'disponivel' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_bool_formatter'],
            ],
            'nome' => [
                'type' => PARAM_TEXT,
                'default' => '',
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'status' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'prazo' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'datacriacao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'datamoficacao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'cursopresencial' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_bool_formatter'],
            ],
            'frequencia' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'media' => [
                'type' => PARAM_FLOAT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'keywords' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'cargahoraria' => [
                'type' => PARAM_FLOAT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_float_formatter'],
            ],
            'descricao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'tipo_solucao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'apresentacao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'objetivos' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'conteudo_programatico' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'nivel_de_complexidade' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'termo_de_aceite' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'ficha_tecnica' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'requisitos' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'criterios_de_avaliacao' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'publico' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'area_subarea' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
        ];
    }

    public static function get_by_codtrilha(int $codtrilha) : ?static {
        return static::get_record(['codtrilha' => $codtrilha]) ?: null;
    }


    public static function upsert_from_migration_table_data(array|object $data) : int {
        $data = (array)$data;
        $data['codtrilha'] = $data['source_id'];
        return parent::upsert_from_migration_table_data($data);
    }

    public function get_formatted_carga_horaria(): string {
        $carga_horaria = $this->get('cargahoraria') ?? 0;
        // $seconds = (float) $carga_horaria * HOURSECS;
        // return format_time($seconds);
        return number_format($carga_horaria, 2, ',', '.');
    }

    public function get_formatted_nome(): string {
        return $this->get('nome');
    }

    public function get_formatted_conteudo_programatico(): string {
        return $this->get('conteudo_programatico');
    }

    public function get_formatted_tipo(): string {
        return $this->get('tipo_solucao');
    }

    public function get_formatted_formato_solucao(): string {
        return $this->get('tipo_solucao');
    }
}
