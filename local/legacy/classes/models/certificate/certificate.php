<?php

namespace local_legacy\models\certificate;

use core\persistent;
use local_legacy\models\abstract_legacy_entity;
use local_legacy\models\curso;
use local_legacy\models\trilha;
use context;
use context_system;

class certificate extends persistent {

    const TABLE = 'legacy_certificate';

    const TYPE_COURSE = 'course';
    const TYPE_TRAIL  = 'trail';

    const CERTIFICATE_COMPONENT_NAME   = 'local_legacy';
    const CERTIFICATE_IMAGE_FILE_AREA  = 'certificate_images';      // first‐page image area
    const CERTIFICATE_IMAGE2_FILE_AREA = 'certificate_images_2';    // second‐page image area
    const CERTIFICATE_ISSUES_FILE_AREA = 'certificate_issues';

    const OUTPUT_OPEN_IN_BROWSER = 0;
    const OUTPUT_FORCE_DOWNLOAD = 1;
    const OUTPUT_SEND_EMAIL = 2;

    // Date Options Const.
    const CERT_ISSUE_DATE = -1;
    const COURSE_COMPLETATION_DATE = -2;
    const COURSE_START_DATE = -3;

    // Grade Option Const.
    const NO_GRADE = 0;
    const COURSE_GRADE = -1;

    // View const.
    const DEFAULT_VIEW = 0;
    const ISSUED_CERTIFCADES_VIEW = 1;
    const BULK_ISSUE_CERTIFCADES_VIEW = 2;


    /**
     * Get the first‐page background image fileinfo template.
     *
     * @param int $certificateid
     * @return array Context-component-filearea-itemid-filepath for first image.
     */
    public static function get_certificate_image_fileinfo($certificateid) {
        return [
            'contextid' => context_system::instance()->id,
            'component' => self::CERTIFICATE_COMPONENT_NAME,
            'filearea'  => self::CERTIFICATE_IMAGE_FILE_AREA,
            'itemid'    => $certificateid,
            'filepath'  => '/'
        ];
    }

    /**
     * Get the second‐page background image fileinfo template.
     *
     * @param int $certificateid
     * @return array Context-component-filearea-itemid-filepath for second image.
     */
    public static function get_certificate_secondimage_fileinfo($certificateid) {
        return [
            'contextid' => context_system::instance()->id,
            'component' => self::CERTIFICATE_COMPONENT_NAME,
            'filearea'  => self::CERTIFICATE_IMAGE2_FILE_AREA,
            'itemid'    => $certificateid,
            'filepath'  => '/'
        ];
    }

    /**
     * Return the definition of properties.
     *
     * @return array
     */
    protected static function define_properties(): array {
        return [
            'solutiontype' => [
                'type'        => PARAM_TEXT,
                'description' => 'Type of solution: course or trail.',
                'null'        => NULL_NOT_ALLOWED,
                'choices'     => [
                    self::TYPE_COURSE,
                    self::TYPE_TRAIL,
                ],
            ],
            'solutionid' => [
                'type'        => PARAM_INT,
                'description' => 'ID of the course or trail.',
                'null'        => NULL_NOT_ALLOWED,
            ],
            'legacyid' => [
                'type'        => PARAM_INT,
                'description' => 'Original certificate ID.',
                'null'        => NULL_NOT_ALLOWED,
            ],
            'name' => [
                'type'        => PARAM_TEXT,
                'description' => 'Certificate name.',
                'null'        => NULL_NOT_ALLOWED,
            ],
            'width' => [
                'type'        => PARAM_INT,
                'description' => 'Page width (mm).',
                'default'     => 297,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'height' => [
                'type'        => PARAM_INT,
                'description' => 'Page height (mm).',
                'default'     => 210,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'certificatetext' => [
                'type'        => PARAM_RAW,
                'description' => 'Main certificate text (HTML).',
                'default'     => '',
                'null'        => NULL_NOT_ALLOWED,
            ],
            'certificatetextformat' => [
                'type'        => PARAM_INT,
                'description' => 'Format of certificatetext.',
                'default'     => 2,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'certificatetextx' => [
                'type'        => PARAM_INT,
                'description' => 'X position for text.',
                'default'     => 10,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'certificatetexty' => [
                'type'        => PARAM_INT,
                'description' => 'Y position for text.',
                'default'     => 50,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'printqrcode' => [
                'type'        => PARAM_BOOL,
                'description' => 'Whether to print a QR code.',
                'default'     => true,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'qrcodefirstpage' => [
                'type'        => PARAM_BOOL,
                'description' => 'Print QR code on first page only.',
                'default'     => true,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'codex' => [
                'type'        => PARAM_INT,
                'description' => 'X position for QR code.',
                'default'     => 10,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'codey' => [
                'type'        => PARAM_INT,
                'description' => 'Y position for QR code.',
                'default'     => 10,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'enablesecondpage' => [
                'type'        => PARAM_BOOL,
                'description' => 'Enable second page.',
                'default'     => false,
                'null'        => NULL_NOT_ALLOWED,
            ],
            'secondpagex' => [
                'type'        => PARAM_INT,
                'description' => 'X position for second page text.',
                'default'     => 10,
                'null'        => NULL_ALLOWED,
            ],
            'secondpagey' => [
                'type'        => PARAM_INT,
                'description' => 'Y position for second page text.',
                'default'     => 50,
                'null'        => NULL_ALLOWED,
            ],
            'secondpagetext' => [
                'type'        => PARAM_RAW,
                'description' => 'Text for second page (HTML).',
                'default'     => null,
                'null'        => NULL_ALLOWED,
            ],
            'secondpagetextformat' => [
                'type'        => PARAM_INT,
                'description' => 'Format of second page text.',
                'default'     => 2,
                'null'        => NULL_ALLOWED,
            ],
        ];
    }

    public function is_solutiontype_course(): bool {
        return $this->get('solutiontype') === self::TYPE_COURSE;
    }

    public function is_solutiontype_trail(): bool {
        return $this->get('solutiontype') === self::TYPE_TRAIL;
    }

    public function get_solution(): ?abstract_legacy_entity {
        $solutionid = (int) $this->get('solutionid');
        if (!$solutionid) {
            return null;
        }
        if ($this->is_solutiontype_course()) {
            return curso::get_by_codcurso($solutionid);
        }
        if ($this->is_solutiontype_trail()) {
            return trilha::get_by_codtrilha($solutionid);
        }
        return null;
    }

    public static function get_by_id(int $id): ?static {
        return static::get_record(['id' => $id]) ?: null;
    }

    /**
     * Return an stdClass ready to be passed directly to edit_form::set_data().
     */
    public function to_formdata(): \stdClass {
        $data = new \stdClass();
        $data->id                    = $this->get('id');
        $data->name                  = $this->get('name');

        $data->certificatetext = [
            'text'   => $this->get('certificatetext'),
            'format' => $this->get('certificatetextformat'),
        ];
        $data->certificatetextx      = $this->get('certificatetextx');
        $data->certificatetexty      = $this->get('certificatetexty');

        $data->secondpagetext = [
            'text'   => $this->get('secondpagetext'),
            'format' => $this->get('secondpagetextformat'),
        ];
        $data->secondpagex           = $this->get('secondpagex');
        $data->secondpagey           = $this->get('secondpagey');

        $data->solutiontype          = $this->get('solutiontype');
        $data->solutionid            = $this->get('solutionid');
        $data->legacyid              = $this->get('legacyid');

        $data->width                 = $this->get('width');
        $data->height                = $this->get('height');

        $data->printqrcode           = $this->get('printqrcode');
        $data->qrcodefirstpage       = $this->get('qrcodefirstpage');
        $data->codex                 = $this->get('codex');
        $data->codey                 = $this->get('codey');

        $data->enablesecondpage      = $this->get('enablesecondpage');

        // Preparing filearea for certificateimage
        $certificate_image = certificate::get_certificate_image_fileinfo($this->get('id'));
        $certificateimage_draftid = file_get_submitted_draft_itemid('certificateimage');

        file_prepare_draft_area(
            $certificateimage_draftid,
            $certificate_image['contextid'],
            $certificate_image['component'],
            $certificate_image['filearea'],
            $certificate_image['itemid'],
            [
                'subdirs'       => false,
                'maxbytes'      => 0,
                'maxfiles'      => 1,
            ]
        );

        $data->certificateimage = $certificateimage_draftid;

        // Preparing filearea for secondimage
        $certificate_secondimage = certificate::get_certificate_secondimage_fileinfo($this->get('id'));
        $secondimage_draftid = file_get_submitted_draft_itemid('secondimage');

        file_prepare_draft_area(
            $secondimage_draftid,
            $certificate_secondimage['contextid'],
            $certificate_secondimage['component'],
            $certificate_secondimage['filearea'],
            $certificate_secondimage['itemid'],
            [
                'subdirs'       => false,
                'maxbytes'      => 0,
                'maxfiles'      => 1,
            ]
        );

        $data->secondimage = $secondimage_draftid;

        return $data;
    }

    public function make_second_page_text(): string {
        $text = "NOME: [NOME_ALUNO] [CPF_DOCINTERNACIONAL]: [LOGIN] UF: [UF]\n";
        $text .= "EMPRESA: [EMPRESA]\n";
        $text .= "CNPJ: [CNPJ]\n";

        if ($this->is_solutiontype_course()) {
            $text .= "[TERMO_CURSO_OU_PROGRAMA]: [NOME_CURSO_OU_PROGRAMA]\n";
        } else {
            $text .= "[TERMO_ITEM]: [NOME_ITEM]\n";
        }

        $text .= "CARGA HORÁRIA: [CARGA_HORARIA]\n";
        $text .= "PERÍODO: [PERIODO] APROVEITAMENTO: [APROVEITAMENTO_EXTENSO]\n";
        $text .= "\n[CONTEUDOPROGRAMATICO]";

        return $text;
    }


    /**
     * Returns the Moodle stored_file for the first‐page certificate image,
     * or null if none exists.
     *
     * @return \stored_file|null
     */
    public function get_first_page_image(): ?\stored_file {
        global $CFG;
        $fs = get_file_storage();
        $fileinfo = self::get_certificate_image_fileinfo($this->get('id'));

        $files = $fs->get_area_files(
            $fileinfo['contextid'],
            $fileinfo['component'],
            $fileinfo['filearea'],
            $fileinfo['itemid'],
            'filename', false
        );

        foreach ($files as $file) {
            if (!$file->is_directory()) {
                return $file;
            }
        }
        return null;
    }

    /**
     * Returns the Moodle stored_file for the second‐page certificate image,
     * or null if none exists.
     *
     * @return \stored_file|null
     */
    public function get_second_page_image(): ?\stored_file {
        global $CFG;
        $fs = get_file_storage();

        $fileinfo = self::get_certificate_secondimage_fileinfo($this->get('id'));

        $files = $fs->get_area_files(
            $fileinfo['contextid'],
            $fileinfo['component'],
            $fileinfo['filearea'],
            $fileinfo['itemid'],
            'filename', false
        );

        foreach ($files as $file) {
            if (!$file->is_directory()) {
                return $file;
            }
        }
        return null;
    }


    public function update_from_formdata(object $data): static {
        $this->set_many([
            'name'                  => $data->name,
            'certificatetext'       => $data->certificatetext['text'],
            'certificatetextformat' => $data->certificatetext['format'],
            'certificatetextx'      => $data->certificatetextx,
            'certificatetexty'      => $data->certificatetexty,
            'secondpagetext'        => $data->secondpagetext['text'],
            'secondpagetextformat'  => $data->secondpagetext['format'],
            'secondpagex'           => $data->secondpagex,
            'secondpagey'           => $data->secondpagey,
            'solutiontype'          => $data->solutiontype,
            'solutionid'            => $data->solutionid,
            'legacyid'              => $data->legacyid,
            'width'                 => $data->width,
            'height'                => $data->height,
            'printqrcode'           => $data->printqrcode,
            'qrcodefirstpage'       => $data->qrcodefirstpage,
            'codex'                 => $data->codex,
            'codey'                 => $data->codey,
            'enablesecondpage'      => $data->enablesecondpage
        ]);

        return $this;
    }

}
