<?php

namespace local_legacy\models\certificate;

use core\persistent;
use local_legacy\models\abstract_legacy_entity;
use local_legacy\models\aluno;
use local_legacy\models\interfaces\certificate_issue_interface;
use local_legacy\models\interfaces\situacao_matricula_interface;
use local_legacy\models\situacao_curso;
use local_legacy\models\situacao_trilha;

class certificate_issue extends persistent implements certificate_issue_interface {

    const TABLE = 'legacy_certificate_issues';

    protected certificate $certificate;
    protected ?situacao_matricula_interface $situacao_matricula;

    protected static function define_properties(): array {
        return [
            'certificateid' => [
                'type' => PARAM_INT,
                'description' => 'ID of the certificate.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'codsituacaoalunocurso' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 0,
                'description' => "Cursos"
            ],
            'codsituacao' => [
                'type' => PARAM_INT,
                'null' => NULL_NOT_ALLOWED,
                'default' => 0,
                'description' => 'Trilhas'
            ],
            'code' => [
                'type' => PARAM_RAW,
                'description' => 'Certificate validation code.',
                'null' => NULL_NOT_ALLOWED,
            ],
        ];
    }

    public function get_certificate() : certificate {
        if(!isset($this->certificate)){
            $this->certificate = new certificate((int) $this->get('certificateid'));
        }
        return $this->certificate;
    }

    protected function fetch_situacao_matricula() : ?situacao_matricula_interface {
        $codsituacaoalunocurso = $this->get('codsituacaoalunocurso');
        if(!empty($codsituacaoalunocurso)){
            return situacao_curso::get_record(['codsituacaoalunocurso' => $codsituacaoalunocurso]) ?: null;
        }

        $codsituacao = $this->get('codsituacao');
        if(!empty($codsituacao)){
            return situacao_trilha::get_record(['codsituacao' => $codsituacao]) ?: null;
        }

        return null;
    }

    public function get_situacao_matricula() : ?situacao_matricula_interface {
        if(!isset($this->situacao_matricula)){
            $this->situacao_matricula = $this->fetch_situacao_matricula();
        }
        return $this->situacao_matricula;
    }

    public static function get_by_code(string $code) : ?static {
        global $DB;

        $select = "code = :code OR code = :code_lower";
        $params = ['code' => $code, 'code_lower' => strtolower($code)];

        if($record = $DB->get_record_select(static::TABLE, $select, $params)){
            return new static(0, $record);
        }

        return null;
    }
}
