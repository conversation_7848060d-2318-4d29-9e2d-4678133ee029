<?php

namespace local_legacy\models;

use local_legacy\util\eadtech_helper;


defined('MOODLE_INTERNAL') || die();

class aluno extends abstract_legacy_entity {
    const TABLE = 'legacy_alunos';

    protected ?bool $is_cpf = null;

    protected function validate_cpf() : bool {
        $cpf = preg_replace('/\D/', '', $this->get('cpf'));

        if (strlen($cpf) !== 11) {
            return false;
        }

        if (preg_match('/^(\d)\1{10}$/', $cpf)) {
            return false;
        }

        $calculate_digit = function (string $base, int $length): int {
            $sum = 0;
            $weight = $length + 1;

            for ($i = 0; $i < $length; $i++) {
                $sum += (int) $base[$i] * $weight--;
            }

            $remainder = $sum % 11;
            return ($remainder < 2) ? 0 : 11 - $remainder;
        };

        $digit1 = $calculate_digit($cpf, 9);
        $digit2 = $calculate_digit($cpf, 10);

        return $digit1 === (int) $cpf[9] && $digit2 === (int) $cpf[10];
    }

    protected function is_cpf() : bool {
        if($this->is_cpf === null){
            $this->is_cpf = $this->validate_cpf();
        }

        return $this->is_cpf;
    }

    public static function define_identifier(): string {
        return 'codaluno';
    }

    protected static function define_model_properties(): array {
        return [
            'userid' => [
                'type' => PARAM_INT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'codaluno' => [
                'type' => PARAM_INT,
                'formatter' => [eadtech_helper::class, 'data_int_formatter'],
            ],
            'nome' => [
                'type' => PARAM_TEXT,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'email' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'estado' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'sexo' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'nascimento' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'escolaridade' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'estadocivil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datacadastro' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'nivelocupacional' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'cpf' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'emailsecundario' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'celular' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'perfil' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'status' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'filial' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'dataadmissao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'dataexpiracao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'dataalteracao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
            'ufsebrae' => [
                'type' => PARAM_TEXT,
                'null' => NULL_ALLOWED,
                'default' => null,
                'formatter' => [eadtech_helper::class, 'data_text_formatter'],
            ],
            'datamodificacao' => [
                'type' => PARAM_INT,
                'default' => 0,
                'formatter' => [eadtech_helper::class, 'data_date_formatter'],
            ],
        ];
    }

    public static function get_by_codaluno(int $codaluno) : ?static {
        return static::get_record(['codaluno' => $codaluno]) ?: null;
    }


    public function get_formatted_cpf() : string {
        $cpf = preg_replace('/\D/', '', $this->get('cpf'));
        $cpf = str_pad($cpf, 11, '0', STR_PAD_LEFT);

        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }

    public function get_formatted_cpf_docinternacional() : string {
        if($this->is_cpf()){
            return "CPF";
        }
        return "DOCUMENTO";
    }

    public function get_formatted_login() : string {
        if($this->is_cpf()){
            return $this->get_formatted_cpf();
        }
        return $this->get('cpf');
    }

    public function get_formatted_nome() : string {
        return $this->get('nome');
    }

    public function get_formatted_uf() : string {
        return $this->get('estado');
    }

    public function get_formatted_empresa_cnpj() : string {
        return "...";
    }

    public function get_formatted_empresa_razao_social() : string {
        return "...";
    }
}
