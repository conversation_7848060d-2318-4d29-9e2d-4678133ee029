<?php

function xmldb_local_degreed_integration_upgrade($oldversion) {
    global $DB;
    $dbman = $DB->get_manager();
    
    if ($oldversion < 2025060100) {

        // Define table degreed_trails to be created.
        $table = new xmldb_table('degreed_trails');

        // Adding fields to table degreed_trails.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('enabled', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('externalid', XMLDB_TYPE_CHAR, '50', null, null, null, null);
        $table->add_field('timechanged', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timesynchronized', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');

        // Adding keys to table degreed_trails.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);

        // Adding indexes to table degreed_trails.
        $table->add_index('idx_sync_dates', XMLDB_INDEX_NOTUNIQUE, ['enabled', 'timechanged', 'timesynchronized']);
        $table->add_index('idx_local_instance', XMLDB_INDEX_NOTUNIQUE, ['courseid', 'enabled', 'externalid']);

        // Conditionally launch create table for degreed_trails.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Degreed_integration savepoint reached.
        upgrade_plugin_savepoint(true, 2025060100, 'local', 'degreed_integration');
    }

      if ($oldversion < 2025060600) {

        // Define field localid to be added to degreed_courses.
        $table = new xmldb_table('degreed_courses');
        $field = new xmldb_field('localid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'externalid');

        // Conditionally launch add field localid.
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        // Degreed_integration savepoint reached.
        upgrade_plugin_savepoint(true, 2025060600, 'local', 'degreed_integration');
    }


    return true;
}
