<?php namespace local_degreed_integration;

defined('MOODLE_INTERNAL') || die();

global $CFG;

use \advanced_testcase;
use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\models\course_completion_sync_record;
use \local_degreed_integration\task\process_course_change;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;
use \local_ssystem\constants\custom_profile_fields;
use \core\event\course_deleted;
use \completion_completion;


class events_test extends advanced_testcase{
    
    /**
     * @group !skip
     * @return void
     */
    public function test_course_creation(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course_disabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 0
                ],
            ]
        ]);

        $course_enabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
            ]
        ]);

        $this->assertEquals(2, count(\core\task\manager::get_adhoc_tasks(process_course_change::class)));
        $this->runAdhocTasks();

        $course_sync_record_disabled = course_sync_record::get_or_create_from_courseid($course_disabled->id);
        $course_sync_record_enabled = course_sync_record::get_or_create_from_courseid($course_enabled->id);

        $this->assertFalse((bool) $course_sync_record_disabled->get('enabled'));
        $this->assertTrue((bool) $course_sync_record_enabled->get('enabled'));
    }

    /**
     * @group !skip
     * @return void
     */
    public function test_course_update(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
            ]
        ]);

        $this->runAdhocTasks();

        $this->assertTrue((bool) course_sync_record::get_or_create_from_courseid($course->id)->get('enabled'));

        // Updating flag
        $course->{"customfield_" . course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME} = 0;
        update_course($course);

        $this->assertEquals(1, count(\core\task\manager::get_adhoc_tasks(process_course_change::class)));
        $this->runAdhocTasks();

        $this->assertFalse((bool) course_sync_record::get_or_create_from_courseid($course->id)->get('enabled'));
    }

    /**
     * @group !skip
     * @return void
     */
    public function test_course_delete(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
            ]
        ]);

        $this->runAdhocTasks();

        $this->assertTrue((bool) course_sync_record::get_or_create_from_courseid($course->id)->get('enabled'));

        // Deleting course
        $sink = $this->redirectEvents();
        delete_course($course, false);
        $course_deleted_event = array_filter($sink->get_events(), function($event){
            return $event::class == course_deleted::class;
        });
        // The events are redirects because at the moment local_customfield observers doesn't handle exception properly
        \local_degreed_integration\observers\courses::course_deleted(reset($course_deleted_event));

        $this->assertFalse((bool) course_sync_record::get_or_create_from_courseid($course->id)->get('enabled'));
    }


    /**
     * @group !skip
     * @return void
     */
    public function test_course_completion(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course_disabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 0
                ],
            ],
            'enablecompletion' => true
        ]);

        $course_enabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
            ],
            'enablecompletion' => true
        ]);

        $this->runAdhocTasks();

        $user = $this->getDataGenerator()->create_user([
            'profile_field_' . custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD => custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER,
        ]);

        // Enrolling users
        $this->getDataGenerator()->enrol_user($user->id, $course_disabled->id, 'student');
        $this->getDataGenerator()->enrol_user($user->id, $course_enabled->id, 'student');

        // Completing courses
        $ccompletion = new completion_completion(['course' => $course_disabled->id, 'userid' => $user->id]);
        $ccompletion->mark_complete();

        $ccompletion = new completion_completion(['course' => $course_enabled->id, 'userid' => $user->id]);
        $ccompletion->mark_complete();

        // Checking completions
        $disabled_completion = course_completion_sync_record::get_record(['courseid' =>  $course_disabled->id, 'userid' => $user->id]);
        $enabled_completion = course_completion_sync_record::get_record(['courseid' =>  $course_enabled->id, 'userid' => $user->id]);

        $this->assertTrue(!!$disabled_completion?->get('timecompleted'));
        $this->assertTrue(!!$enabled_completion?->get('timecompleted'));
    }


    /**
     * @group !skip
     * @return void
     */
    public function test_course_completion_nom_worker(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $course_disabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 0
                ],
            ],
            'enablecompletion' => true
        ]);

        $course_enabled = $this->getDataGenerator()->create_course([
            'customfields' => [
                [
                    'shortname' => course_sync_record::SYNC_TO_DEGREED_FIELD_SHORTNAME,
                    'value' => 1
                ],
            ],
            'enablecompletion' => true
        ]);

        $this->runAdhocTasks();

        $user = $this->getDataGenerator()->create_user();

        // Enrolling users
        $this->getDataGenerator()->enrol_user($user->id, $course_disabled->id, 'student');
        $this->getDataGenerator()->enrol_user($user->id, $course_enabled->id, 'student');

        // Completing courses
        $ccompletion = new completion_completion(['course' => $course_disabled->id, 'userid' => $user->id]);
        $ccompletion->mark_complete();

        $ccompletion = new completion_completion(['course' => $course_enabled->id, 'userid' => $user->id]);
        $ccompletion->mark_complete();

        // Checking completions
        $disabled_completion = course_completion_sync_record::get_record(['courseid' =>  $course_disabled->id, 'userid' => $user->id]);
        $enabled_completion = course_completion_sync_record::get_record(['courseid' =>  $course_enabled->id, 'userid' => $user->id]);

        $this->assertEmpty($disabled_completion);
        $this->assertEmpty($enabled_completion);
    }
}
