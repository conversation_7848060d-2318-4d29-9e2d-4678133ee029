<?php

use \local_degreed_integration\degreed\degreed_client;
use Guzzle<PERSON>ttp\Handler\MockHandler;
use Guzzle<PERSON>ttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Middleware;
use \GuzzleHttp\Client;
use \Psr\Http\Message\ResponseInterface;

class degreed_test_client extends degreed_client {
    
    const REGEX_TOKEN = '#/oauth/token$#';
    const REGEX_CREATE_COURSE = '#/v2/content/courses$#';
    const REGEX_UPDATE_COURSE = '#/v2/content/courses/[^/]+$#';
    const REGEX_REPLACE_SKILLS = '#/v2/content/[^/]+/relationships/skills$#';
    const REGEX_CREATE_COMPLETION = '#v2/completions$#';
    const REGEX_UPDATE_COMPLETION = '#v2/completions/[^/]+$#';

    private MockHandler $mock_handler;
    private array $history = [];
    protected $pre_request_callback;

    /**
     * Class constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->mock_handler = new MockHandler([]);
        $handler_stack = HandlerStack::create($this->mock_handler);

        $history = Middleware::history($this->history);
        $handler_stack->push($history);

        $this->client = new Client(['handler' => $handler_stack]);

        // Default callback for appending mocked responses
        $this->set_pre_request_callback([static::class, 'make_mock_handler']);
    }

    public function append_mocked_response(Closure|Response|Exception $handler){
        $this->mock_handler->append($handler);
    }

    public function reset_mocked_responses(){
        $this->mock_handler->reset();
    }

    public function get_last_request() : ?array {
        // $last_index = array_key_last($this->history);
        // return $last_index === null ? null : $this->history[$last_index];
        
        return array_pop($this->history);
    }

    public function reset_request_history() {
        $this->history = [];
    }

    /**
     * The Closure will receive the following arguments when called
     * (degreed_client $client, string $method, string $uri, array $options)
     *
     * @param Closure $callback
     * @return void
     */
    public function set_pre_request_callback(callable $callback){
        $this->pre_request_callback = $callback;
    }



    public static function make_mock_handler(degreed_test_client $client, string $method, $uri = '', array $options = []){
        $method = strtoupper($method);

        $is_get = $method === "GET";
        $is_post = $method === "POST";
        $is_patch = $method === "PATCH";
        $is_delete = $method === "DELETE";

        $response = match (true) {
            $is_post && preg_match(self::REGEX_TOKEN, $uri) => self::make_mocked_login_handler(),
            $is_delete && preg_match(self::REGEX_TOKEN, $uri) => self::make_mocked_logout_handler(),
            $is_post && preg_match(self::REGEX_CREATE_COURSE, $uri) => self::make_create_course_handler(),
            $is_patch && preg_match(self::REGEX_UPDATE_COURSE, $uri) => self::make_update_course_handler(),
            $is_patch && preg_match(self::REGEX_REPLACE_SKILLS, $uri) => self::make_upsert_course_skills_handler(),
            $is_post && preg_match(self::REGEX_CREATE_COMPLETION, $uri) => self::make_create_course_completion_handler(),
            $is_patch && preg_match(self::REGEX_UPDATE_COMPLETION, $uri) => self::make_update_course_completion_handler(),
            default => new Response(404, [], null),
        };

        $client->append_mocked_response($response);
    }

    public static function make_mocked_login_handler() : Closure {
        return function(Request $request){
            return new Response(200, [], json_encode([
                "access_token" => "z56Q0BK8tXiN9VzJmJ9evD7VsG9u12DSF-0h0uZgc7Gx1AY4TBYbBHi75eg4Jp_NAyPl41sEpOtfEe9nHI_HpBnXnCy9NsH-6mtXrky4P8o0pA4QL2tY18cFh2iAevlZ5SizHfPF4FcCM64xKMXGLyanTOeC6ziRgBhxX0pPx7kWATjJjft-DhqW5he8_cKcPsn1w9OuzrBJqeWbI-I_t_Djg4euuVXBLbeC-Ak2JTG_CYILg46mKKMcTB_RvA806r35haeSQWgVXfiFu8tvqCmgyBOxdWYDOd3QtS_5bv6C131CGU8rMn0pfJclkE2ypAmtEDv7h-L6blSVlMvxkqck2poShDLGXUx43YTSrtqdKoSY5DNSQWvFFkVGJ14gfwl-pO6pJJKDoXwDu4V6zcwFUVsr5AdSDwl_xuLuKMqFJgr0f4zStP9jwNOV9QT8lS0KNxdNDHHay560UYSRjp_FLXh5R1KYC-moqOWkp8nZ_LsdV8ZhF3rV8AYzZ9c67RyD7mxl9NGklqiJyZUQOwwQqipuZBo9cSewD8FazWtvkYYUpZM7IKYXi3jwpC0F_ptmJfW0f4DYboODFDj6lN7k2qL6fFWXxaGKNLK1ZlLVvPbKzvkjXlBJCzOb3KkdHsNTmV44Os8qrJ_1IKS6WIsi08yQ9xAdDYjU1hWaw3VCEyjee-g3UqcPXobfqfayAhsZRlAMo8wSzVqJcHZN-LJNEO3hIP1ZI5UOTi7ftYdojsKHrbnvef1b8zUlU62FqAabMICWDIoqs4ra3GDXIY7Fv1tTChJlFkdLHdIxMG6-HjivdWY0Y7HibqKwjBuKVRNwNFcHTuJxEFyfQH283yCq0lQIynYGuw0aeNv2Mw-NPTWsxu_J8jwbJQndR31_P6iAzRUj5WF5V-j8uYx7wO0y3eixmOfKWwKfMuSTpoiTPkaQ",
                "token_type" => "bearer",
                "expires_in" => 5183999,
                "refresh_token" => "2bc229ee622b4a81967d1c169846ce56d92a75b649f84c25b51f7d8f2d7e1444"
            ]));
        };
    }

    public static function make_create_course_handler() : Closure {
        return function(Request $request){
            $payload = json_decode((string) $request->getBody(), true);
            $payload['data']['id'] = md5($payload['data']['attributes']['external-id']);
            $payload['data']['type'] = "content/courses";
            $payload['data']['relationships'] = [];
            return new Response(201, [], json_encode($payload));
        };
    }

    public static function make_update_course_handler() : Closure {
        return function(Request $request){
            $payload = json_decode((string) $request->getBody(), true);
            $uri = $request->getUri()->getPath();
            $segments = explode('/', rtrim($uri, '/'));

            $payload['data']['id'] = end($segments);
            $payload['data']['type'] = "content/courses";
            $payload['data']['relationships'] = [];
            return new Response(200, [], json_encode($payload));
        };
    }

    public static function make_upsert_course_skills_handler() : Closure {
        return function(Request $request){
            return new Response(201, [], null);
        };
    }

    public static function make_create_course_completion_handler() : Closure {
        return function(Request $request){
            $payload = json_decode((string) $request->getBody(), true);

            $id = $payload['data']['attributes']['user-id'] . $payload['data']['attributes']['content-id'];
            $payload['data']['id'] = md5($id);
            $payload['data']['type'] = "completions";
            return new Response(201, [], json_encode($payload));
        };
    }

    public static function make_update_course_completion_handler() : Closure {
        return function(Request $request){
            $payload = json_decode((string) $request->getBody(), true);
            $uri = $request->getUri()->getPath();
            $segments = explode('/', rtrim($uri, '/'));

            $payload['data']['id'] = end($segments);
            $payload['data']['type'] = "completions";
            return new Response(200, [], json_encode($payload));
        };
    }

    public static function make_mocked_logout_handler() : Closure {
        return function(Request $request){
            return new Response(200, [], 'OK');
        };
    }

    public function request(string $method, $uri = '', array $options = []): ResponseInterface {
        if(isset($this->pre_request_callback)){
            $callable = $this->pre_request_callback;
            $callable($this, $method, $uri, $options);
        }
        return parent::request($method, $uri, $options);
    }
}