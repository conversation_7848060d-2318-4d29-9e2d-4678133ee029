<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8"/>
        <title>Test Documentation</title>
        <style>
            body {
                text-rendering: optimizeLegibility;
                font-variant-ligatures: common-ligatures;
                font-kerning: normal;
                margin-left: 2em;
                background-color: #ffffff;
                color: #000000;
            }

            body > ul > li {
                font-family: Source Serif Pro, PT Sans, Trebuchet MS, Helvetica, Arial;
                font-size: 2em;
            }

            h2 {
                font-family: Tahoma, Helvetica, Arial;
                font-size: 3em;
            }

            ul {
                list-style: none;
                margin-bottom: 1em;
            }
        </style>
    </head>
    <body>
        <h2 id="local_degreed_integration\course_completion_integration_processor_test">course_completion_integration_processor_test (local_degreed_integration\course_completion_integration_processor_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Course completion integration processor</li>
        </ul>
        <h2 id="local_degreed_integration\course_integration_processor_test">course_integration_processor_test (local_degreed_integration\course_integration_processor_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Course integration processor</li>
        </ul>
        <h2 id="local_degreed_integration\degreed_entities_test">degreed_entities_test (local_degreed_integration\degreed_entities_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Course entity from course sync record</li>
            <li style="color: #555753;">✓ Course entity to api</li>
            <li style="color: #555753;">✓ Api to course entity</li>
            <li style="color: #555753;">✓ Course completion entity from course sync record</li>
            <li style="color: #555753;">✓ Api to course completion entity</li>
            <li style="color: #555753;">✓ Course completion entity to api</li>
        </ul>
        <h2 id="local_degreed_integration\events_test">events_test (local_degreed_integration\events_test)</h2>
        <ul>
            <li style="color: #555753;">✓ Course creation</li>
            <li style="color: #555753;">✓ Course update</li>
            <li style="color: #555753;">✓ Course delete</li>
            <li style="color: #555753;">✓ Course completion</li>
            <li style="color: #555753;">✓ Course completion nom worker</li>
        </ul>
    </body>
</html>