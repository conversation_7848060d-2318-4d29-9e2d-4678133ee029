<?php namespace local_degreed_integration;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/fixtures/degreed_test_client.php');
require_once(__DIR__ . '/fixtures/degreed_test_trait.php');

use \advanced_testcase;
use degreed_test_client;
use degreed_test_trait;

use \local_degreed_integration\models\course_sync_record;
use \local_degreed_integration\models\course_completion_sync_record;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;
use \local_degreed_integration\config;
use \completion_completion;
use \local_degreed_integration\processors\course_integration_processor;
use \local_degreed_integration\processors\course_completion_integration_processor;
use \local_degreed_integration\event\degreed_course_completion_created;
use \local_degreed_integration\event\degreed_course_completion_updated;
use local_ssystem\constants\custom_profile_fields;

class course_completion_integration_processor_test extends advanced_testcase{

    use degreed_test_trait;

    /**
     * @group !skip
     * @return void
     */
    public function test_course_completion_integration_processor(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        // Creating users
        $user1 = $this->getDataGenerator()->create_user([
            'profile_field_' . custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD => custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER,
        ]);
        $user2 = $this->getDataGenerator()->create_user([
            'profile_field_' . custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD => custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER,
        ]);
        $user3 = $this->getDataGenerator()->create_user([
            'profile_field_' . custom_profile_fields::OCCUPATIONAL_PROFILE_FIELD => custom_profile_fields::OCCUPATIONAL_PROFILE_WORKER,
        ]);

        // Creating courses
        $course1 = $this->getDegreedGenerator()->create_degreed_course();
        $course2 = $this->getDegreedGenerator()->create_degreed_course();

        $this->runAdhocTasks();
        
        $course_processor = new course_integration_processor(new degreed_test_client());
        $course_processor->execute();

        // Enrolling users and completing courses
        foreach ([$course1, $course2] as $course) {
            foreach ([$user1, $user2] as $user) {
                $this->getDataGenerator()->enrol_user($user->id, $course->id, 'student');
                $ccompletion = new completion_completion(['course' => $course->id, 'userid' => $user->id]);
                $ccompletion->mark_complete();
            }
        }

        // Adding an extra completion (Simulating a deleted course completion)
        $cc_sync3 = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course1->id,
            'userid' => $user3->id,
        ]);
        $cc_sync3->set('timecompleted', time());
        $cc_sync3->mark_as_changed();
        $cc_sync3->save();

        // Executing processor
        $sink = $this->redirectEvents();

        $cc_processor = new course_completion_integration_processor(new degreed_test_client());
        $cc_processor->execute();

        $events = $sink->get_events();
        $this->stopEventRedirection();

        $create_events = array_filter($events, function($event){
            return is_a($event, degreed_course_completion_created::class);
        });

        $this->assertCount(5, $create_events, "Five degreed_course_completion_created events should have been fired");

        // Checking syncronizations
        foreach ([$course1, $course2] as $course) {
            foreach ([$user1, $user2] as $user) {
                $cc_sync = course_completion_sync_record::get_or_create_from_course_completion((object)[
                    'course' => $course->id,
                    'userid' => $user->id,
                ]);

                $this->assertFalse(empty($cc_sync->get('id')));
                $this->assertTrue($cc_sync->get('timesynchronized') > 0);
            }
        }

        $cc_sync = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course1->id,
            'userid' => $user3->id,
        ]);

        $this->assertTrue($cc_sync->get('timesynchronized') > 0);

        // Disabling course 2 sync
        sleep(1);
        $course_sync2 = course_sync_record::get_or_create_from_courseid($course2->id);
        $course_sync2->set('enabled', false);
        $course_sync2->save();

        // Mocking a completion on course 2 by user 3
        $cc_sync3_2 = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course2->id,
            'userid' => $user3->id,
        ]);
        $cc_sync3_2->set('timecompleted', time());
        $cc_sync3_2->mark_as_changed();
        $cc_sync3_2->save();

        // Updating completion of user 2 on course 1
        $cc_sync2_1 = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course1->id,
            'userid' => $user2->id,
        ]);
        $cc_sync2_1->set('timecompleted', time());
        $cc_sync2_1->mark_as_changed();
        $cc_sync2_1->save();

        // Executing processor
        $sink = $this->redirectEvents();

        $cc_processor = new course_completion_integration_processor(new degreed_test_client());
        $cc_processor->execute();

        $events = $sink->get_events();
        $this->stopEventRedirection();

        $this->assertCount(1, $events, "Only the update event should be fired here");
        $this->assertTrue(is_a(end($events), degreed_course_completion_updated::class));


        // Checking completion for user 2 on course 3
        $cc_sync3_2_updated = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course2->id,
            'userid' => $user3->id,
        ]);

        $this->assertTrue(empty($cc_sync3_2_updated->get('timesynchronized')), "Should not sync a disabled course");


        $cc_sync2_1_updated = course_completion_sync_record::get_or_create_from_course_completion((object)[
            'course' => $course1->id,
            'userid' => $user2->id,
        ]);

        $this->assertTrue((int) $cc_sync2_1_updated->get('timesynchronized') > (int) $cc_sync2_1->get('timesynchronized'));
    }

    protected function stopEventRedirection(){
        \phpunit_util::stop_event_redirection();
    }
         
}
