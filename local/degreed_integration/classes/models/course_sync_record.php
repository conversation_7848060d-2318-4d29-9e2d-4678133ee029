<?php namespace local_degreed_integration\models;

use \local_degreed_integration\models\entity_sync_record;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;

use \core\event\course_created;
use \core\event\course_updated;
use \core\event\course_deleted;

use \context_course;

use local_degreed_integration\util\format_helper;
class course_sync_record extends entity_sync_record {

    const TABLE = 'degreed_courses';

    const SYNC_TO_DEGREED_FIELD_SHORTNAME = 'cadastrar_na_degreed';
    
    /** In memory cache for the course object */
    protected ?object $course = null;
    
    protected static function define_properties() {
        $definition = parent::define_properties();

        $definition['courseid'] = [
            'type' => PARAM_INT,
        ];
        $definition['enabled'] = [
            'type' => PARAM_BOOL,
            'default' => true,
        ];

        $definition['localid'] = [
            'type' => PARAM_TEXT,
            'default' => null,
            'null' => NULL_ALLOWED,
        ];        

        return $definition;
    }

    protected function before_create() {
        if(!$this->get('localid')){
            $this->raw_set('localid', $this->generate_local_id());
        }
    }

    protected function generate_local_id() : string {
        $courseid = (int) $this->get('courseid');

        if(format_helper::is_trail($courseid)){
            return "lfsebrae-t-$courseid";
        }

        return "lfsebrae-c-$courseid";
    }

    /**
     * Retrieves or creates a new instance from the course id
     *
     * @param integer $courseid
     * @return static
     */
    public static function get_or_create_from_courseid(int $courseid) : ?static {
        $raw = ['courseid' => $courseid];
        return static::get_record($raw) ?: new static(0, (object) $raw);
    }

    /**
     * Returns the course instance.
     *
     * @return object|null
     */
    public function get_course() : ?object {
        global $DB;

        if(empty($this->course)){
            $this->course = $DB->get_record('course', ['id' => $this->get('courseid')]) ?: null;
        }
        return $this->course;
    }

    /**
     * Returns the course context.
     *
     * @return object|null
     */
    public function get_course_context() : ?context_course {
        return context_course::instance($this->get('courseid'), IGNORE_MISSING) ?: null;
    }


    public static function get_pending_records_generator(int $limit = 0) : \Generator {
        global $DB;
        $select = 'enabled = 1 AND timechanged > timesynchronized';
        $recordset = $DB->get_recordset_select(static::TABLE, $select, null, 'timechanged DESC', '*', 0, $limit);

        foreach ($recordset as $record) {
            yield new static(0, $record);
        }

        $recordset->close();
    }

    public function update_enabled() : static {
        $this->raw_set('enabled', static::is_sync_enabled_for_course($this->get('courseid')));
        return $this;
    }

    public static function is_sync_enabled_for_course(int $courseid) : bool {
        return (bool) custom_course_field::get_field_value(static::SYNC_TO_DEGREED_FIELD_SHORTNAME, $courseid)?->get_value();
    }

    public static function upsert_from_event(course_created|course_updated|course_deleted $event) : static {
        $record = static::get_or_create_from_courseid($event->objectid);

        $record->update_enabled();
        $record->mark_as_changed();
        $record->save();
        
        return $record;
    }

    public static function upsert_from_courseid(int $courseid) : static {
        $record = static::get_or_create_from_courseid($courseid);
        
        $record->update_enabled();
        $record->mark_as_changed();
        $record->save();
        
        return $record;
    }

    public static function get_externalid_from_courseid(int $courseid) : ?string {
        global $DB;
        return $DB->get_field(static::TABLE, 'externalid', ['courseid' => $courseid]) ?: null;
    }

    public function is_enabled() : bool {
        return (bool) $this->get('enabled');
    }
}
