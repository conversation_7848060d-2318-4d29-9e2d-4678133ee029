<?php namespace local_degreed_integration\degreed;

use \local_degreed_integration\config;
use \moodle_exception;
use \local_degreed_integration\exceptions\degreed_integration_exception;
use \local_degreed_integration\exceptions\degreed_unauthorized_exception;
use \local_degreed_integration\degreed\entities\content\course;


use \GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use local_degreed_integration\degreed\entities\completion\course_completion;
use local_degreed_integration\degreed\entities\pathway\pathway;
use local_degreed_integration\degreed\entities\pathway\pathway_section;
use \Psr\Http\Message\ResponseInterface;
class degreed_client{

    const EXPIRATION_THRESHOLD = 30;
    const FORMAT_JSON = 'json';

    const HTTP_BAD_REQUEST = 400;
    const HTTP_UNAUTHORIZED = 401;
    const HTTP_FORBIDDEN = 403;
    const HTTP_NOT_FOUND = 404;

    const DEFAULT_INTERVAL_BETWEEN_REQUESTS = 1;

    protected ?string $access_token;
    protected ?string $token_type;
    protected ?string $refresh_token;
    protected int $token_expiration = 0;

    private ?string $client_id;
    private ?string $client_secret;

    private string $token_url;
    private string $api_url;

    protected Client $client;

    protected int $interval_between_requests = self::DEFAULT_INTERVAL_BETWEEN_REQUESTS;

    public function __construct() {
        $this->client_id = config::get_client_id();
        $this->client_secret = config::get_client_secret();
        $this->token_url = config::get_api_token_url();
        $this->api_url = config::get_api_base_url();

        $this->client = new Client();
    }

    function __destruct() {
        $this->logout();
    }

    protected function make_api_url($path) : string {
        return $this->api_url . '/' . trim($path, '/'); 
    }

    public function set_interval_between_requests(int $interval){
        $this->interval_between_requests = $interval;
    }

    protected function wait(){
        sleep($this->interval_between_requests);
    }

    public function request(string $method, $uri = '', array $options = []): ResponseInterface {
        $this->wait();
        return $this->client->request($method, $uri, $options);
    }

    public function is_logged_in(){
        return !empty($this->access_token) && $this->token_expiration >= time();
    }

    /**
     * Attempts a login on Degreed, retrieving the access token
     * 
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @return void
     */
    public function login(){
        try {
            $response = $this->request('POST', $this->token_url, [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->client_id,
                    'client_secret' => $this->client_secret,
                    'scope' => $this->get_token_scope(),
                ],
                'headers' => [
                    'accept' => 'application/json',
                    'Content-Type: application/x-www-form-urlencoded',
                ],
            ]);
    
            if($response->getStatusCode() != 200){
                throw new degreed_integration_exception('exception:unable_to_get_access_token', null, 400);
            }
    
            $body = json_decode($response->getBody());
    
            $this->access_token = $body->access_token;
            $this->refresh_token = $body?->refresh_token;
            $this->token_type = $body?->token_type;
            $this->token_expiration = time() - self::EXPIRATION_THRESHOLD + $body->expires_in;

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            $code = $rex->hasResponse() ? $rex->getResponse()->getStatusCode() : 400;
            throw new degreed_integration_exception('exception:unable_to_get_access_token', $rex->getMessage(), $code);
        }        
    }

    /**
     * If logged in, attempts do logout, destroying the current
     * access token.
     *
     * @return void
     */
    public function logout(){
        if(!$this->is_logged_in()){
            return;
        }

        try {
            $this->request('DELETE', $this->token_url, [
                'headers' => [
                    'authorization' => $this->get_authorization_token(),
                ],
            ]);
    
            
        } catch (RequestException $rex) {
            debugging($rex->getMessage(), DEBUG_DEVELOPER);
        } finally {
            $this->access_token = null;
            $this->refresh_token = null;
            $this->token_type = null;
            $this->token_expiration = 0;
        }
    }

    protected function get_authorization_token() : string {
        if(!$this->is_logged_in()){
            $this->login();
        }

        return $this->token_type . ' ' . $this->access_token;
    }

    protected function get_token_scope() : string {
        return implode(',', [
            'content:read',
            'content:write',
            'completions:read',
            'completions:write',
            // 'pathways:read',
            // 'pathways:write',
        ]);
    }

    /**
     * Gets a course from Degreed
     * 
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param string $id
     * @return course
     */
    public function get_course(string $id) : course {
        $url = $this->make_api_url('/v2/content/courses/' . $id);
        
        try {
            $response = $this->request('GET', $url, [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => $this->get_authorization_token(),
                ],
            ]);
    
            return course::from_api($response->getBody());

        } catch (RequestException $rex) {
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                case self::HTTP_NOT_FOUND:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:course_not_found');
                    break;
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_getting_course');
                    break;
            }
        }
    }

    /**
     * Creates a new course on Degreed
     *
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param course $course (gets updated)
     * @return void
     */
    public function create_course(course &$course) {
        $url = $this->make_api_url('/v2/content/courses');

        try {
            $response = $this->request('POST', $url, [
                'body' => $course->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $course->update_from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_creating_course');
                    break;
            }
        }        
    }

    public function update_course(course &$course) {
        $url = $this->make_api_url('/v2/content/courses/' . $course->id);

        try {
            $response = $this->request('PATCH', $url, [
                'body' => $course->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $course->update_from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());

            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_updating_course');
                    break;
            }
        }
    }

    public function upsert_course_skills(course &$course){
        // $url = $this->make_api_url('/v2/content/' . $course->id . '/relationships/skills');
        $url = $this->make_api_url('/v2/content/' . $course->id . '/relationships/skills');

        try {
            $response = $this->request('PATCH', $url, [
                'body' => json_encode(['data' => $course->get_skills()]),
                'headers' => [
                    'authorization' => $this->get_authorization_token(),
                    'content-type' => 'application/json',
                ],
            ]);

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_upserting_course_skills');
                    break;
            }
        }
    }

    /**
     * Gets a course completion from Degreed by it's ID
     * 
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param string $id
     * @return course_completion
     */
    public function get_course_completion(string $id) {
        $url = $this->make_api_url('/v2/completions/' . $id);

        try {
            $response = $this->request('GET', $url, [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => $this->get_authorization_token(),
                ],
            ]);
    
            return course_completion::from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());

            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;

                case self::HTTP_NOT_FOUND:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:course_completion_not_found');
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_getting_course_completion');
                    break;
            }
        }
    }

    /**
     * Creates a new course completion on degreed
     * 
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param course_completion $completion
     */
    public function create_course_completion(course_completion &$completion) {
        $url = $this->make_api_url('/v2/completions');

        try {
            $response = $this->request('POST', $url, [
                'body' => $completion->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $completion->update_from_api($response->getBody());

        } catch (RequestException $rex) {
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_creating_course_completion');
                    break;
            }
        }
    }

    /**
     * Updates a course completion on degreed
     * 
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param course_completion $completion
     */
    public function update_course_completion(course_completion &$completion) {
        $url = $this->make_api_url('/v2/completions/' . $completion->id);

        try {
            $response = $this->request('PATCH', $url, [
                'body' => $completion->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $completion->update_from_api($response->getBody());

        } catch (RequestException $rex) {
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_updating_course_completion');
                    break;
            }
        }
    }

    public function set_client(Client $client){
        $this->client = $client;
    }



    /**
     * Creates a new pathway on Degreed
     *
     * @throws \local_degreed_integration\exceptions\degreed_integration_exception
     * @throws \local_degreed_integration\exceptions\degreed_unauthorized_exception
     * @param pathway $trail (gets updated)
     * @return void
     */
    public function create_pathway(pathway $trail) {
        $url = $this->make_api_url('/v2/pathways');

        try {
            $response = $this->request('POST', $url, [
                'body' => $trail->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $trail = pathway::from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_creating_pathway');
                    break;
            }
        }        
    }

    public function update_pathway(pathway $trail) {
        $url = $this->make_api_url('/v2/pathways/' . $trail->id);

        try {
            $response = $this->request('PATCH', $url, [
                'body' => $trail->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $trail = pathway::from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());

            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_updating_pathway');
                    break;
            }
        }
    }

    public function get_all_pathway_tags(pathway $trail) : array {
        $url = $this->make_api_url('/v2/pathways/' . $trail->id . '/tags');

        try {
            $response = $this->request('GET', $url, [
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                ],
            ]);

            $response = json_decode($response->getBody(), true);

            if(!isset($response['data'])){
                throw new \InvalidArgumentException("Malformed Degreed's API Response");
            }

            $tags = [];
            foreach ($response['data'] as $tag) {
                $tags[$tag['id']] = $tag['attributes']['tag'];
            }

            return $tags;

        } catch (RequestException $rex) {
            print_r($rex->getMessage());

            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_listing_pathway_tags');
                    break;
            }
        }
    }

    public function delete_pathway_tags(pathway $trail, array $tags) {
        $url = $this->make_api_url('/v2/pathways/' . $trail->id . '/relationships/tags');

        $body = [
            'data' => array_map(fn($tag) => ['id' => $tag, 'type' => 'skills'], $tags),
        ];

        try {
            $this->request('DELETE', $url, [
                'body' => $body,
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
            ]);

        } catch (RequestException $rex) {
            print_r($rex->getMessage());

            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_updating_pathway');
                    break;
            }
        }
    }

    public function add_pathway_tags(pathway $trail){
        $url = $this->make_api_url('/v2/pathways/' . $trail->id . '/relationships/tags');

        try {
            $this->request('POST', $url, [
                'body' => json_encode(['data' => $trail->get_skills()]),
                'headers' => [
                    'authorization' => $this->get_authorization_token(),
                    'content-type' => 'application/json',
                ],
            ]);

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_upserting_course_skills');
                    break;
            }
        }
    }


    public function create_pathway_section(pathway $trail, pathway_section $section) {
        $url = $this->make_api_url('/v2/pathways' . $trail->id . '/sections');

        try {
            $response = $this->request('POST', $url, [
                'body' => $section->to_api(),
                'headers' => [
                  'accept' => 'application/json',
                  'authorization' => $this->get_authorization_token(),
                  'content-type' => 'application/json',
                ],
              ]);

            $section = pathway::from_api($response->getBody());

        } catch (RequestException $rex) {
            print_r($rex->getMessage());
            switch ($rex?->getResponse()?->getStatusCode()) {
                case self::HTTP_UNAUTHORIZED:
                    throw new degreed_unauthorized_exception();
                    break;
                
                default:
                    throw degreed_integration_exception::from_request_exception($rex, 'exception:error_creating_pathway');
                    break;
            }
        }        
    }



}