<?php namespace local_degreed_integration\degreed\entities\completion;

use \local_degreed_integration\degreed\interfaces\degreed_entity_interface;
use \local_degreed_integration\degreed\traits\degreed_date_trait;
use \local_degreed_integration\degreed\traits\degreed_user_trait;
use \local_degreed_integration\models\course_completion_sync_record;
use \local_degreed_integration\degreed\entities\abstract_entity;

abstract class completion extends abstract_entity {

    use degreed_user_trait;

    const CONTENT_TYPE = null;

    public string $user_id;
    public string $user_identifier_type;
    public string $content_id;
    public string $content_id_type;
    public ?string $content_type = null;
    public string $completed_at;
    public ?bool $is_verified = true;
    public ?int $questions_correct = 0;
    public ?float $percentile = 0.0;

    protected ?int $instance_id;

    public function get_type() : string {
        return 'Completion';
    }

    public function get_api_resource_name() : string {
        return 'completions';
    }

    protected function define_to_api_attributes() : array {
        return [
            'user-id' => $this->user_id,
            'user-identifier-type' => $this->user_identifier_type,
            'content-id' => $this->content_id,
            'content-id-type' => $this->content_id_type,
            'content-type' => $this->content_type,
            'completed-at' => $this->completed_at,
            'is-verified' => $this->is_verified,
            'questions-correct' => $this->questions_correct,
            'percentile' => $this->percentile
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id' => $data['id'],
            'user_id' => $attributes['user-id'],
            'user_identifier_type' => $attributes['user-identifier-type'],
            'content_id' => $attributes['content-id'],
            'content_id_type' => $attributes['content-id-type'],
            'content_type' => $attributes['content-type'] ?? null,
            'completed_at' => $attributes['completed-at'],
            'is_verified' => $attributes['is-verified'] ?? true,
            'questions_correct' => $attributes['questions-correct'] ?? 0,
            'percentile' => $attributes['percentile'] ?? 0.0, 
        ];
    }
}
