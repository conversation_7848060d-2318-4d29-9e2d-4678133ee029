<?php namespace local_degreed_integration\degreed\entities\content;

use \local_degreed_integration\models\course_completion_sync_record;
use \local_degreed_integration\degreed\entities\skill;
use \local_degreed_integration\models\entity_sync_record;
use \local_degreed_integration\models\course_sync_record;
use \tool_lfxp\helpers\custom_fields\course\custom_course_field;
use \local_ssystem\constants\custom_course_fields;

use \local_degreed_integration\exceptions\degreed_integration_exception;
use \moodle_url;
use \coding_exception;
use local_degreed_integration\util\image_helper;

class course extends abstract_content {

    const COURSE_DURATION_FIELD_SHORTNAME = 'workload';

    public ?string $id;
    public string $external_id;
    public ?string $title = null;
    public ?string $summary = null;
    public ?string $publish_date = null;
    public string $url;
    public ?bool $obsolete = false;
    public ?string $image_url = null;
    public ?string $language = null;
    public ?float $duration = null;
    public ?string $duration_type = null;
    public ?float $cost_units = null;
    public ?string $cost_unit_type = null;
    public ?string $format = null;
    public ?string $difficulty = null;
    public ?string $video_url = null;
    public ?int $continuing_education_units = null;
    public ?string $owner_id = null;
    public ?string $owner_type = null;
    public ?array $visibility_groups = [];

    /** @var \local_degreed_integration\degreed\entities\skill[] */
    protected array $skills = [];

    protected ?int $courseid;
    protected ?object $customfields = null;

    public function get_type() : string {
        return 'Course';
    }

    public function get_api_resource_name() : string {
        return 'courses';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id' => $this->external_id,
            'title' => $this->title,
            'summary' => $this->summary,
            'publish-date' => $this->publish_date,
            'url' => $this->url,
            'obsolete' => $this->obsolete,
            'image-url' => $this->image_url,
            'language' => $this->language,
            'duration' => $this->duration,
            'duration-type' => $this->duration_type,
            'cost-units' => $this->cost_units,
            'cost-unit-type' => $this->cost_unit_type,
            'format' => $this->format,
            'difficulty' => $this->difficulty,
            'video-url' => $this->video_url,
            'continuing-education-units' => $this->continuing_education_units,
            'owner-id' => $this->owner_id,
            'owner-type' => $this->owner_type,
            // 'visibility-groups' => $this->visibility_groups // Causes problems with update and its unused
        ];
    }


    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id' => $data['id'],
            'external_id' => $attributes['external-id'],
            'title' => $attributes['title'] ?? null,
            'url' => $attributes['url'],
            'owner_id' => $attributes['owner-id'],
            'owner_type' => $attributes['owner-type'],
            'summary' => $attributes['summary'] ?? null,
            'publish_date' => $attributes['publish-date'] ?? null,
            'obsolete' => $attributes['obsolete'] ?? false,
            'image_url' => $attributes['image-url'] ?? null,
            'language' => $attributes['language'] ?? null,
            'duration' => $attributes['duration'] ?? null,
            'duration_type' => $attributes['duration-type'] ?? null,
            'cost_units' => $attributes['cost-units'] ?? null,
            'cost_unit_type' => $attributes['cost-unit-type'] ?? null,
            'format' => $attributes['format'] ?? null,
            'difficulty' => $attributes['difficulty'] ?? null,
            'video_url' => $attributes['video-url'] ?? null,
            'continuing_education_units' => $attributes['continuing-education-units'] ?? null,
            'visibility_groups' => $attributes['visibility-groups'] ?? null,
        ];
    }

    /*
    {
        "inputId": 43648484,
        "inputType": "Course",
        "title": "Conhecendo o sistema Sebrae - ESSENCIAL - 8h",
        "description": "O curso apresenta o conhecimento essencial sobre o Sebrae aos participantes, abordando sua criação, atuação e impacto no desenvolvimento de pequenos negócios e na promoção da cultura empreendedora. Além disso, serão discutidas as principais formas de atuação do Sebrae, incluindo sua interação com a Lei Geral da Micro e Pequena Empresa, o acesso ao crédito e a exploração de novos mercados por meio do Sistema Sebrae. Durante o curso, será oferecida uma imersão no ambiente de trabalho da instituição, possibilitando aos participantes uma visão completa e abrangente de sua cultura, políticas, práticas e objetivos.",
        "imageUrl": "https://universidade.sebrae.com.br//App/Api/Training/Training/GetActivityImageBase64?idTraining=5286&typeTraining=OnlineCourse",
        "dateCreated": "2024-10-04T20:30:16.8028164Z",
        "dateModified": "2024-11-12T18:48:32.7108456Z",
        "organizationId": 901713,
        "obsolete": false,
        "fileManaged": false,
        "durationUnits": 8.0000,
        "durationUnitType": "hours",
        "durationISO": "P1D",
        "durationHours": 8,
        "url": "https://universidade.sebrae.com.br//app/Student/Training/Panel/Course?FriendlyUrl=conhecendo-o-sistema-sebrae-essencial&isFromDegreed=true",
        "externalId": "ucsebraeead-C-5652",
        "details": "{\"Db\":{\"Reviewed\":1,\"CountNum\":1,\"Units\":8.00,\"UnitType\":\"hours\",\"IsMOOC\":0,\"Origin\":\"v2API\"}}",
        "publishDate": "2024-01-08T14:21:01.41Z",
        "tags": [
            {
                "tagId": 4568304,
                "name": "Onboarding&#8203;",
                "position": 1,
                "isFollowing": false,
                "title": "Onboarding&#8203;",
                "isFocused": false,
                "isMentoring": false,
                "internalUrl": "/resource/tag?id=4568304",
                "resourceId": 4568304,
                "resourceType": "Tag"
            }
        ],
        "orgContentMetadata": {},
        "accessible": false,
        "durationDisplay": "8 Horas",
        "internalUrl": "/courses/conhecendo-o-sistema-sebrae-essencial-8h?d=43648484",
        "providerId": 51150,
        "providerName": "Cursos UC Sebrae",
        "providerUrl": "https://uc.sebrae.com.br/",
        "organizationName_Secure": "Universidade Sebrae",
        "learningMinutes": 480.00,
        "providerSourceId": 51150,
        "institutionId": -1,
        "institutionName": "Internal",
        "isAccredited": false,
        "unitType": "hours",
        "hasBrokenUrl": false,
        "inputDetailsWrapper": {
            "db": {}
        },
        "inputDetailsObj": {},
        "organizationName": "Universidade Sebrae",
        "hasPrimaryContact": false
    }
     */
    public static function from_course_sync_record(course_sync_record $sync_record) : static {
        if(!$instance = $sync_record->get_course()){
            throw new degreed_integration_exception('exception:missing_course');
        }

        $course = new static([
            'id' => $sync_record->get('externalid') ?: null,
            'courseid' => $instance->id,
            'title' => $instance->fullname,
            'summary' => $instance->summary,
            'external_id' => $sync_record->get('localid'),
            'publish_date' => self::format_date_from_timestamp($instance->timecreated), // VALIDAR
            'image_url' => image_helper::get_course_image_url($instance),
            'url' => (new moodle_url('/course/view.php', ['id' => $instance->id]))->out(false),
        ]);

        $course->load_course_duration();
        $course->load_course_difficulty();
        $course->load_course_format();
        $course->load_course_cost();
        $course->load_course_continuing_education_units();

        return $course;
    }

    protected function load_skills(){
        if(empty($this->customfields)){
            $this->load_customfields();
        }
        $this->skills = skill::from_course_customfields($this->customfields);
    }

    protected function load_customfields(){
        $course = (object) ['id' => $this->courseid];
        custom_course_field::load_custom_fields($course);

        $this->customfields = $course->customfields;
    }

    public function get_skills() : array {
        if(empty($this->skills)){
            $this->load_skills();
        }
        return $this->skills;
    }

    public function load_course_duration() {
        if(empty($this->customfields)){
            $this->load_customfields();
        }

        $field_shortname = self::COURSE_DURATION_FIELD_SHORTNAME;
        $duration = intval($this->customfields->$field_shortname?->get_value());

        if(empty($duration)){
            return;
        }

        /** @var object {"value" : float, "unit" : string} */
        $duration = self::format_duration($duration);

        $this->duration = $duration->value;
        $this->duration_type = $duration->unit;
    }

    public function load_course_difficulty() {
        if(empty($this->customfields)){
            $this->load_customfields();
        }

        $field_shortname = custom_course_fields::COMPLEXITY_LEVEL;
        $this->difficulty = $this->customfields->$field_shortname?->export_value();
    }

    public function load_course_format() {
        if(empty($this->customfields)){
            $this->load_customfields();
        }

        $field_shortname = custom_course_fields::SOLUTION_FORMAT;
        $this->format = $this->customfields->$field_shortname?->export_value();
    }

    public function load_course_cost() {
        return;
    }

    public function load_course_continuing_education_units() {
        return;
    }
}
