<?php

namespace local_degreed_integration\degreed\entities\content;

class article extends abstract_content {

    // $id, $external_id, $title and $summary are defined on parent

    public ?string $format = null;
    public ?string $image_url = null;
    public ?int $num_words = null;
    public ?string $owner_id = null;
    public ?string $owner_type = null;
    public ?string $publish_date = null;
    public string $url;


    public function get_type() : string {
        return 'Article';
    }

    public function get_api_resource_name() : string {
        return 'articles';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id' => $this->external_id,
            'title' => $this->title,
            'format' => $this->format,
            'image-url' => $this->image_url,
            'num-words' => $this->num_words,
            'owner-id' => $this->owner_id,
            'owner-type' => $this->owner_type,
            'publish-date' => $this->publish_date,
            'url' => $this->url,
        ];
    }


    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id' => $data['id'],
            'external_id' => $attributes['external-id'],
            'title' => $attributes['title'] ?? null,
            'format' => $attributes['format'] ?? null,
            'image_url' => $attributes['image-url'] ?? null,
            'num_words' => $attributes['num-words'] ?? null,
            'owner_id' => $attributes['owner-id'],
            'owner_type' => $attributes['owner-type'],
            'publish_date' => $attributes['publish-date'] ?? null,
            'url' => $attributes['url'],
        ];
    }
}
