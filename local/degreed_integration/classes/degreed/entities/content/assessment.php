<?php

namespace local_degreed_integration\degreed\entities\content;

class assessment extends abstract_content {
    public string  $url;
    public ?string $image_url      = null;
    public ?string $language       = null;
    public int     $num_questions;
    public ?string $provider_code  = null;
    public ?string $publish_date   = null;
    public ?string $owner_id       = null;
    public ?string $owner_type     = null;

    public function get_type() : string {
        return 'Assessment';
    }

    public function get_api_resource_name() : string {
        return 'assessments';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'   => $this->external_id,
            'title'         => $this->title,
            'summary'       => $this->summary,
            'url'           => $this->url,
            'image-url'     => $this->image_url,
            'language'      => $this->language,
            'num-questions' => $this->num_questions,
            'provider-code' => $this->provider_code,
            'publish-date'  => $this->publish_date,
            'owner-id'      => $this->owner_id,
            'owner-type'    => $this->owner_type,
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['title']           ?? null,
            'summary'       => $attributes['summary']         ?? null,
            'url'           => $attributes['url']             ?? null,
            'image_url'     => $attributes['image-url']       ?? null,
            'language'      => $attributes['language']        ?? null,
            'num_questions' => $attributes['num-questions']   ?? null,
            'provider_code' => $attributes['provider-code']   ?? null,
            'publish_date'  => $attributes['publish-date']    ?? null,
            'owner_id'      => $attributes['owner-id']        ?? null,
            'owner_type'    => $attributes['owner-type']      ?? null,
        ];
    }
}
