<?php

namespace local_degreed_integration\degreed\entities\content;

class podcast extends abstract_content {
    
    public ?string $provider_code   = null;
    public ?bool   $obsolete        = null;
    public ?float  $duration        = null;
    public ?string $duration_type   = null;
    public ?string $format          = null;
    public ?string $feed_url        = null;
    public ?string $publish_date    = null;
    public ?string $image_url       = null;
    public string  $url;
    public ?string $author          = null;
    public ?string $language        = null;
    public ?string $owner_id        = null;
    public ?string $owner_type      = null;

    public function get_type() : string {
        return 'Podcast';
    }

    public function get_api_resource_name() : string {
        return 'podcasts';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'     => $this->external_id,
            'title'           => $this->title,
            'summary'         => $this->summary,
            'provider-code'   => $this->provider_code,
            'obsolete'        => $this->obsolete,
            'duration'        => $this->duration,
            'duration-type'   => $this->duration_type,
            'format'          => $this->format,
            'feed-url'        => $this->feed_url,
            'publish-date'    => $this->publish_date,
            'image-url'       => $this->image_url,
            'url'             => $this->url,
            'author'          => $this->author,
            'language'        => $this->language,
            'owner-id'        => $this->owner_id,
            'owner-type'      => $this->owner_type,
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['title']           ?? null,
            'summary'       => $attributes['summary']         ?? null,
            'provider_code' => $attributes['provider-code']   ?? null,
            'obsolete'      => $attributes['obsolete']        ?? null,
            'duration'      => isset($attributes['duration']) ? floatval($attributes['duration']) : null,
            'duration_type' => $attributes['duration-type']   ?? null,
            'format'        => $attributes['format']          ?? null,
            'feed_url'      => $attributes['feed-url']        ?? null,
            'publish_date'  => $attributes['publish-date']    ?? null,
            'image_url'     => $attributes['image-url']       ?? null,
            'url'           => $attributes['url']             ?? null,
            'author'        => $attributes['author']          ?? null,
            'language'      => $attributes['language']        ?? null,
            'owner_id'      => $attributes['owner-id']        ?? null,
            'owner_type'    => $attributes['owner-type']      ?? null,
        ];
    }
}
