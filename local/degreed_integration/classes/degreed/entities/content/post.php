<?php

namespace local_degreed_integration\degreed\entities\content;

class post extends abstract_content {

    public string  $url;
    public ?string $image_url     = null;
    public ?string $language      = null;
    public ?float  $duration      = null;
    public ?string $duration_type = null;

    public function get_type() : string {
        return 'Post';
    }

    public function get_api_resource_name() : string {
        return 'posts';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'   => $this->external_id,
            'title'         => $this->title,
            'summary'       => $this->summary,
            'url'           => $this->url,
            'image-url'     => $this->image_url,
            'language'      => $this->language,
            'duration'      => $this->duration,
            'duration-type' => $this->duration_type,
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['title']        ?? null,
            'summary'       => $attributes['summary']      ?? null,
            'url'           => $attributes['url']          ?? null,
            'image_url'     => $attributes['image-url']    ?? null,
            'language'      => $attributes['language']     ?? null,
            'duration'      => isset($attributes['duration']) ? floatval($attributes['duration']) : null,
            'duration_type' => $attributes['duration-type'] ?? null,
        ];
    }
}
