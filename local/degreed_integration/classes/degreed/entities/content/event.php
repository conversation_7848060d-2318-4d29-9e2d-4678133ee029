<?php

namespace local_degreed_integration\degreed\entities\content;

class event extends abstract_content {
    
    public string  $url;
    public ?bool   $obsolete       = null;
    public ?string $image_url      = null;
    public ?string $language       = null;
    public ?string $format         = null;
    public ?float  $duration       = null;
    public ?string $duration_type  = null;
    public ?string $date           = null;
    public ?string $publish_date   = null;

    public function get_type() : string {
        return 'Event';
    }

    public function get_api_resource_name() : string {
        return 'events';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'   => $this->external_id,
            'name'         => $this->title,
            'summary'       => $this->summary,
            'url'           => $this->url,
            'obsolete'      => $this->obsolete,
            'image-url'     => $this->image_url,
            'language'      => $this->language,
            'format'        => $this->format,
            'duration'      => $this->duration,
            'duration-type' => $this->duration_type,
            'date'          => $this->date,
            'publish-date'  => $this->publish_date,
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];

        return [
            'id'            => $data['id'],
            'external_id'   => $attributes['external-id'],
            'title'         => $attributes['name']           ?? null,
            'summary'       => $attributes['summary']         ?? null,
            'url'           => $attributes['url']             ?? null,
            'obsolete'      => $attributes['obsolete']        ?? null,
            'image_url'     => $attributes['image-url']       ?? null,
            'language'      => $attributes['language']        ?? null,
            'format'        => $attributes['format']          ?? null,
            'duration'      => isset($attributes['duration'])
                                 ? floatval($attributes['duration'])
                                 : null,
            'duration_type' => $attributes['duration-type']   ?? null,
            'date'          => $attributes['date']            ?? null,
            'publish_date'  => $attributes['publish-date']    ?? null,
        ];
    }
}
