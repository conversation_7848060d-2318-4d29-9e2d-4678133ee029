<?php

namespace local_degreed_integration\degreed\entities\content;

class task extends abstract_content {
    public ?string $provider_code  = null;
    public string  $url;
    public ?bool   $obsolete       = null;
    public ?string $image_url      = null;
    public ?string $language       = null;
    public ?float  $duration       = null;
    public ?string $duration_type  = null;

    public function get_type() : string {
        return 'Task';
    }

    public function get_api_resource_name() : string {
        return 'tasks';
    }

    protected function define_to_api_attributes() : array {
        return [
            'external-id'    => $this->external_id,
            'title'          => $this->title,
            'summary'        => $this->summary,
            'provider-code'  => $this->provider_code,
            'url'            => $this->url,
            'obsolete'       => $this->obsolete,
            'image-url'      => $this->image_url,
            'language'       => $this->language,
            'duration'       => $this->duration,
            'duration-type'  => $this->duration_type,
        ];
    }

    protected static function from_api_data(array $data) : array {
        $attributes = $data['attributes'];
        
        return [
            'id'             => $data['id'],
            'external_id'    => $attributes['external-id'],
            'title'          => $attributes['title']          ?? null,
            'summary'        => $attributes['summary']        ?? null,
            'provider_code'  => $attributes['provider-code']  ?? null,
            'url'            => $attributes['url']            ?? null,
            'obsolete'       => $attributes['obsolete']       ?? null,
            'image_url'      => $attributes['image-url']      ?? null,
            'language'       => $attributes['language']       ?? null,
            'duration'       => isset($attributes['duration'])
                                 ? floatval($attributes['duration'])
                                 : null,
            'duration_type'  => $attributes['duration-type']  ?? null,
        ];
    }
}
