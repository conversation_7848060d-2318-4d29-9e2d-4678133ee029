<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use moodle_exception;
use Exception;
use html_writer;
use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\constants;

/**
 * Trait class_user_enrol_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_user_enrol_trait
{

    public function get_orderby_relationship_array()
    {
        global $DB;

        return [
            'fullname' => $DB->sql_concat('u.firstname', "' '", 'u.lastname'),
            'email' => 'u.email',
            'cpf' => 'u.username',
            'startdate' => 'ue.timestart',
            'enddate' => 'ue.timeend',
            'enrolperiod' => 'CASE WHEN ue.timeend > 0 THEN (ue.timeend - ue.timestart) / 86400 ELSE 0 END',
            'situation' => 'oue.situation',
            'status' => 'ue.status'
        ];
    }

    public function get_orderby_relationship(string $field): string
    {
        $relationships = $this->get_orderby_relationship_array();

        return $relationships[$field] ?? throw new moodle_exception('error:invalid_orderby_field', 'local_offermanager', $field);
    }

    /**
     * Verifica se um usuário possui uma inscrição na instância de inscrição.
     *
     * @param int $userid ID do usuário.
     * @return bool Retorna true se o usuário estiver inscrito, false caso contrário.
     */
    public function user_has_enrolment(int $userid): bool
    {
        return offer_user_enrol_model::record_exists_select(
            'offerclassid = :offerclassid AND userid = :userid',
            [
                'offerclassid' => $this->get('id'),
                'userid' => $userid,
            ]
        );
    }

    /**
     * Verifica se existem inscrições associadas a instância de inscrição.
     *
     * @return bool
     */
    public function has_user_enrolments()
    {
        return offer_user_enrol_model::record_exists_select(
            'offerclassid = :offerclassid',
            [
                'offerclassid' => $this->get('id')
            ]
        );
    }

    /**
     * Retorna as inscrições associadas a instância de inscrição.
     *
     * @return array
     */
    public function get_user_enrolments()
    {
        global $DB;

        return $DB->get_records(
            'user_enrolments',
            [
                'enrolid' => $this->get('enrolid')
            ]
        );
    }

    /**
     * Retorna as inscrições associadas à instância de inscrição.
     *
     * @param string $searchstring Termo de busca (opcional)
     * @param string $fieldstring Campo de busca: 'name', 'email' ou 'cpf'
     * @param array $excludeduserids IDs de usuários a serem excluídos
     * @return array Lista de usuários matriculados
     */
    public function get_enroled_users(
        string $searchstring = '',
        string $fieldstring = 'name',
        array $excludeduserids = [],
        bool $limit = false
    ): array {
        global $DB;

        $search_fields = [
            'name' => $DB->sql_concat('u.firstname', "' '", 'u.lastname'),
            'email' => 'u.email',
            'username' => 'u.username'
        ];

        $fullname_fields = [
            'name' => $DB->sql_concat('u.firstname', "' '", 'u.lastname'),
            'email' => $DB->sql_concat($DB->sql_concat('u.firstname', "' '", 'u.lastname'),"' '",$DB->sql_concat("'('", 'u.email', "')'")),
            'username' => $DB->sql_concat($DB->sql_concat('u.firstname', "' '", 'u.lastname'),"' '",$DB->sql_concat("'('", 'u.username', "')'"))
        ];

        $search_field = $search_fields[$fieldstring] ?? $search_fields['name'];
        $fullname_field = $fullname_fields[$fieldstring] ?? $search_fields['name'];

        $params = [
            $this->get('enrolid')
        ];

        $conditions = 'u.id > 1 AND ue.enrolid = ?';

        if ($searchstring) {
            $like = $DB->sql_like($search_field, '?', false, false);
            $conditions .= " AND {$like}";
            $params[] = '%' . $searchstring . '%';
        }

        if (!empty($excludeduserids)) {
            list($insql, $inparams) = $DB->get_in_or_equal($excludeduserids, SQL_PARAMS_QM, '?', false);
            $conditions .= ' AND ue.userid ' . $insql;
            $params = array_merge($params, $inparams);
        }

        $limit = $limit ? 'LIMIT 0, 200' : '';

        return $DB->get_records_sql(
            "SELECT
                DISTINCT u.id as id,
                {$fullname_field} as fullname
            FROM {user} u
                JOIN {user_enrolments} ue ON (ue.userid = u.id)
            WHERE {$conditions}
            ORDER BY u.firstname, u.lastname
            {$limit}
            ",
            $params
        );
    }

    /**
     * Retorna as quantidade de inscrições associadas a turma.
     *
     * @return array
     */
    public function get_offer_user_enrolments()
    {
        return offer_user_enrol_model::get_records(
            [
                'offerclassid' => $this->get('id')
            ]
        );
    }

    /**
     * Retorna as inscrições associadas a instância de inscrição.
     *
     * @param int $userid User id to get enrolment
     * @return stdClass
     */
    public function get_user_enrolment(int $userid)
    {
        global $DB;

        return $DB->get_record(
            'user_enrolments',
            [
                'enrolid' => $this->get('enrolid'),
                'userid' => $userid
            ]
        );
    }

    /**
     * Retorna a inscrição ativa na turma.
     *
     * @param int $userid User id to get enrolment
     * @return stdClass
     */
    public function get_user_offer_user_enrolment(int $userid)
    {
        return offer_user_enrol_model::get_record(
            [
                'offerclassid' => $this->get('id'),
                'userid' => $userid
            ]
        );
    }

    /**
     * Retorna a instância que relacionada a inscrição com a turma.
     *
     * @param int $ueid User Enrolment id to get enrolment
     * @return stdClass
     */
    public static function get_ue_offer_user_enrolment(int $ueid)
    {
        return offer_user_enrol_model::get_record(
            [
                'ueid' => $ueid
            ]
        );
    }

    /**
     * Retorna as quantidade de inscrições associadas a turma.
     *
     * @return int
     */
    public function count_user_enrolments()
    {
        return offer_user_enrol_model::count_records_select(
            'offerclassid = :offerclassid',
            [
                'offerclassid' => $this->get('id')
            ]
        );
    }

    /**
     * Busca inscrições da turma aplicando múltiplos filtros combinados, com paginação.
     *
     * @param int[] $userids Lista de IDs de usuários para filtrar (opcional)
     * @param int $page Página atual (1-based)
     * @param int $perpage Quantidade por página
     * @param string $orderby Campo para ordenação
     * @return array ['total' => int, 'enrolments' => offer_user_enrol_model[]]
     */
    public function get_filtered_enrolments(
        array $userids = [],
        int $page = 0,
        int $perpage = 20,
        string $orderby = 'fullname',
        string $direction = 'ASC'
    ): array {
        global $DB;

        $select = 'oue.userid > 1 AND ';
        $wheres = ['oue.offerclassid = ?'];
        $params = [$this->get('id')];

        if (!empty($userids)) {
            list($inuseridsql, $inuseridparams) = $DB->get_in_or_equal($userids);
            $wheres[] = "oue.userid $inuseridsql";
            $params = array_merge($params, $inuseridparams);
        }
        $select .= implode(' AND ', $wheres);
        $page = max(0, $page);
        $offset = max(0, $page * $perpage);

        $total = $DB->count_records_sql(
            "SELECT COUNT(1)
            FROM {local_offermanager_ue} oue
            WHERE {$select}
            ",
            $params
        );

        $orderby_relationship = $this->get_orderby_relationship($orderby);
        $orderby_relationship = $DB->sql_order_by_text($orderby_relationship);
        
        $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';

        $enrolment_ids = $DB->get_fieldset_sql(
            "SELECT
                oue.id
            FROM {local_offermanager_ue} oue
            JOIN {user_enrolments} ue ON (oue.ueid = ue.id)
            JOIN {user} u ON (u.id = oue.userid)
            WHERE {$select}
            ORDER BY {$orderby_relationship} {$direction}
            LIMIT {$perpage}
            OFFSET {$offset}
            ",
            $params
        );

        $enrolments = array_map(
            function ($id) {
                return new offer_user_enrol_model($id);
            },
            $enrolment_ids
        );

        return [
            'page' => $page,
            'per_page' => $perpage,
            'total' => $total,
            'enrolments' => $enrolments
        ];
    }

    /**
     * Desmatricula um usuário em uma instância de inscrição específica.
     *
     * @param int $userid ID do usuário que terá a inscrição apagada.
     * @return bool Retorna true se a exclusão da inscrição for bem-sucedida, false caso contrário.
     */
    public function unenrol_user($userid)
    {
        global $DB;

        $instance = $this->get_enrol_instance();
        if (!$instance) {
            throw new moodle_exception('error:enrol_instance_not_found', 'local_offermanager');
        }

        $plugin = $this->get_plugin();
        if (!$plugin) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $ue = $DB->get_record('user_enrolments', [
            'enrolid' => $instance->id,
            'userid' => $userid
        ]);

        if (!$ue) {
            throw new moodle_exception('error:user_not_enrolled', 'local_offermanager');
        }

        try {
            $plugin->unenrol_user($instance, $userid);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Inscreve um usuário em uma instância de inscrição específica.
     *
     * @param int $userid ID do usuário a ser inscrito.
     * @param int|null $roleid ID da função que será atribuída ao usuário (opcional).
     * @param int $timestart Timestamp de início da inscrição (opcional, padrão: 0).
     * @param int $timeend Timestamp de término da inscrição (opcional, padrão: 0).
     * @param int $status Status da inscrição (opcional, padrão: ENROL_USER_ACTIVE).
     * @param bool|null $recovergrades Indica se as notas devem ser recuperadas (opcional).
     * @return bool Retorna true se a inscrição for bem-sucedida, false caso contrário.
     */
    public function enrol_user(
        $userid,
        $roleid = null,
        $timestart = 0,
        $timeend = 0,
        $status = ENROL_USER_ACTIVE,
        $recovergrades = null
    ) {
        global $DB;

        $instance = $this->get_enrol_instance();

        if (!$instance) {
            throw new moodle_exception('error:enrol_instance_not_found', 'local_offermanager');
        }

        $plugin = $this->get_plugin();
        if (!$plugin) {
            throw new moodle_exception('error:enrol_plugin_not_found', 'local_offermanager');
        }

        $offercourse = $this->get_offer_course();

        if ($offercourse->has_active_user_enrolment($userid)) {
            $user = $DB->get_record('user', ['id' => $userid]);

            $user_fullname = fullname($user);
            throw new moodle_exception('error:user_has_active_enrolment', 'local_offermanager', '', $user_fullname);
        }

        list($timestart, $timeend) = $this->get_enrol_period($timestart, $timeend);

        try {
            $plugin->enrol_user(
                $instance,
                $userid,
                $roleid,
                $timestart,
                $timeend,
                $status,
                $recovergrades
            );

            $ue = $DB->get_record(
                'user_enrolments',
                [
                    'userid' => $userid,
                    'enrolid' => $instance->id
                ]
            );

            $offer_ue = offer_user_enrol_model::get_by_ueid($ue->id);

            return $offer_ue && $offer_ue->get('id');
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Verifica se o usuário está inscrito na instância de inscrição e se sua inscrição está ativa.
     *
     * @param int $userid ID do usuário.
     * @return bool Verdadeiro se o usuário estiver inscrito ativamente.
     */
    public function is_user_enrolled_and_active(int $userid): bool
    {
        global $DB;

        $instance = $this->get_enrol_instance();

        $plugin = $this->get_plugin();

        if (!$plugin || !enrol_is_enabled($instance->enrol)) {
            return false;
        }

        if (!$instance) {
            return false;
        }

        $now = time();

        $sql = "
            SELECT ue.id
            FROM {user_enrolments} ue
                JOIN {enrol} e ON e.id = ue.enrolid
            WHERE ue.userid = :userid
                AND ue.enrolid = :enrolid
                AND ue.status = :status
                AND e.status = :enrolstatus
                AND ue.timestart <= :now1
                AND (ue.timeend = 0 OR ue.timeend >= :now2)
        ";

        $params = [
            'userid' => $userid,
            'enrolid' => $instance->id,
            'status' => ENROL_USER_ACTIVE,
            'enrolstatus' => ENROL_INSTANCE_ENABLED,
            'now1' => $now,
            'now2' => $now,
        ];

        return $DB->record_exists_sql($sql, $params);
    }

    public function suspend_user_enrol($userid): bool
    {

        $instance = $this->get_enrol_instance();
        $plugin = $this->get_plugin();
        $plugin->update_user_enrol($instance, $userid, ENROL_USER_SUSPENDED);

        $user_enrol = $this->get_user_enrolment($userid);

        return $user_enrol->status == ENROL_USER_SUSPENDED;
    }

    public function process_unsuccessful_enrolments()
    {
        $expired_offer_user_enrols = $this->get_expired_offer_user_enrols();
        if (!$expired_offer_user_enrols) {
            return false;
        }

        foreach ($expired_offer_user_enrols as  $expired_offer_user_enrol) {
            $expired_offer_user_enrol->process_expiration();
            $expired_offer_user_enrol->read();
            $offer_user_enrol_id = $expired_offer_user_enrol->get('id');
            $situation = $expired_offer_user_enrol->get('situation');
            mtrace("-- Processed expirations of offer_user_enrol ID {$offer_user_enrol_id} to situation ID {$situation}");
        }
    }

    public function get_expired_offer_user_enrols(): array
    {
        global $DB;
        $expired_offer_user_enrol_ids = $DB->get_fieldset_sql(
            "SELECT oue.id
            FROM {user_enrolments} ue
            JOIN {" . offer_user_enrol_model::TABLE . "} oue ON (oue.ueid = ue.id)
            WHERE ue.enrolid = :enrolid
                AND ue.timeend > 0
                AND ue.timeend < :now
                AND (oue.situation = :enroled OR oue.situation = :in_progress)
            ",
            [
                'enrolid' => $this->get('enrolid'),
                'now' => time(),
                'enroled' => constants::OFFER_USER_ENROL_SITUATION_ENROLED,
                'in_progress' => constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS,
            ]
        );
        if (!$expired_offer_user_enrol_ids) {
            return [];
        }

        return array_map(
            function ($expired_offer_user_enrol_id) {
                return new offer_user_enrol_model($expired_offer_user_enrol_id);
            },
            $expired_offer_user_enrol_ids
        );
    }

    /**
     * Retorna o período de inscrição formatado.
     *
     * @param int $timestart Timestamp de início da inscrição (opcional, padrão: 0).
     * @param int $timeend Timestamp de término da inscrição (opcional, padrão: 0).
     * @return string Período de inscrição formatado.
     */
    public function get_formated_enrol_period(int $timestart = 0, int $timeend = 0): string
    {
        list($timestart, $timeend) = $this->get_enrol_period($timestart, $timeend);

        $startdate = userdate($timestart, get_string('strftimedate', 'langconfig'));

        if ($timeend === 0) {
            return get_string('enrol_period_format_start', 'local_offermanager', ['startdate' => $startdate]);
        }

        $enddate = userdate($timeend, get_string('strftimedate', 'langconfig'));
        return html_writer::div(
            get_string('enrolperiod', 'local_offermanager') . ': ' . get_string('enrol_period_format', 'local_offermanager', (object) ['startdate' => $startdate, 'enddate' => $enddate]),
            'mx-2'
        );
    }

    /**
     * Retorna o período de inscrição.
     *
     * @param int $timestart Timestamp de início da inscrição (opcional, padrão: 0).
     * @param int $timeend Timestamp de término da inscrição (opcional, padrão: 0).
     * @return array Contendo o início e o fim do período de inscrição.
     */
    public function get_enrol_period(int $timestart = 0, int $timeend = 0): array
    {
        $enabled_enrol_period = $this->get_mapped_field('enableenrolperiod') ?: 0;
        $enrolperiod = $this->get_mapped_field('enrolperiod') ?: 0;
        $enrolstartdate = $this->get_mapped_field('startdate') ?: 0;
        $enrolenddate = $this->get_mapped_field('enddate') ?: 0;

        $now = time();

        $timestart = $timestart ? $timestart : ($enrolstartdate < $now ? $now : $enrolstartdate);
        $timeend = $timeend ? $timeend : (($enabled_enrol_period && $enrolperiod) ? ($enrolperiod + $timestart) : $enrolenddate);

        if ($timeend && $timestart > $timeend) {
            $timestart = $timeend;
        }

        return [$timestart, $timeend];
    }
}
