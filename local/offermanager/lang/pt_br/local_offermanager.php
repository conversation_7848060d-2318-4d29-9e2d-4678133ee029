<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Pacote de idioma em português para o Offer Manager
 *
 * @package    local_offermanager
 * @category   string
 * @copyright  2025 2015 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Gerenciador de Ofertas';
$string['offermanager:manage'] = 'Gerenciar Ofertas';
$string['offermanager:viewparticipants'] = 'Permite visualizar a tela de usuários matriculados';
$string['offermanager:manageparticipants'] = 'Permite gerenciar a tela de usuários matriculados';

$string['manage'] = 'Gerenciar Ofertas';
$string['offer'] = 'Oferta';
$string['offers'] = 'Ofertas';
$string['offer_created'] = 'Oferta criada com sucesso!';
$string['offer_updated'] = 'Oferta atualizada com sucesso!';
$string['offer_deleted'] = 'Oferta excluída com sucesso!';
$string['offer_courses'] = 'Curso da Oferta';
$string['offer_course_plural'] = 'Cursos da Oferta';
$string['offer_audiences'] = 'Público-alvo da Oferta';
$string['offer_audiences_plural'] = 'Públicos-alvo da Oferta';
$string['offer_class'] = 'Turma';
$string['offer_class_plural'] = 'Turmas';
$string['notaccessible_title'] = 'Turma não acessível';
$string['notaccessible_heading'] = 'Turma não acessível';
$string['notaccessible_message'] = 'A turma "{$a->classname}" não está acessível pois o número mínimo de {$a->minrequired} inscrições ainda não foi atingido.<br> Procure a administração do seu sistema para maiores informações.';

$string['situation:enroled'] = 'Inscrito';
$string['situation:in_progress'] = 'Em Andamento';
$string['situation:approved'] = 'Aprovado';
$string['situation:completed'] = 'Concluído';
$string['situation:user_canceled'] = 'Cancelado pelo Usuário';
$string['situation:admin_canceled'] = 'Cancelado pelo Administrador';
$string['situation:failed'] = 'Reprovado';
$string['situation:not_completed'] = 'Não Concluído';
$string['situation:abandoned'] = 'Abandono';
$string['situation:unknown'] = 'Situação Desconhecida';

$string['event:offercreated'] = 'Oferta Criada';
$string['event:offerupdated'] = 'Oferta Atualizada';
$string['event:offerdeleted'] = 'Oferta Excluída';
$string['event:offeractivated'] = 'Oferta Ativada';
$string['event:offerinactivated'] = 'Oferta Desativada';
$string['event:offeraudiencecreated'] = 'Relação entre oferta e público-alvo criada';
$string['event:offeraudienceupdated'] = 'Relação entre oferta e público-alvo atualizada';
$string['event:offeraudiencedeleted'] = 'Relação entre oferta e público-alvo excluída';
$string['event:offercoursecreated'] = 'Relação entre oferta e curso criada';
$string['event:offercourseupdated'] = 'Relação entre oferta e curso atualizada';
$string['event:offercoursedeleted'] = 'Relação entre oferta e curso excluída';
$string['event:offercourseactivated'] = 'Relação entre oferta e curso ativada';
$string['event:offercourseinactivated'] = 'Relação entre oferta e curso desativada';
$string['event:offerclasscreated'] = 'Turma criada';
$string['event:offerclassupdated'] = 'Turma atualizada';
$string['event:offerclassactivated'] = 'Turma ativada';
$string['event:offerclassinactivated'] = 'Turma desativada';
$string['event:offerclassdeleted'] = 'Turma excluída';
$string['event:offer_audiences_updated'] = 'Grupo de público-alvo atualizado';
$string['event:offerclassteachersupdated'] = 'Professor da turma {$a->} foi atualizado';
$string['event:offerclassaccessible'] = 'Turma acessível';
$string['event:offerclasscyclestarted'] = 'Ciclo operacional da turma iniciado';
$string['event:offerclasscyclefinished'] = 'Ciclo operacional da turma finalizado';
$string['event:offer_user_enrol_enroled'] = 'Usuário matriculado na oferta';
$string['event:offer_user_enrol_in_progress'] = 'Matrícula do usuário na oferta em andamento';
$string['event:offer_user_enrol_canceled'] = 'Matrícula do usuário na oferta cancelada';
$string['event:offer_user_enrol_approved'] = 'Matrícula do usuário na oferta aprovada';
$string['event:offer_user_enrol_completed'] = 'Matrícula do usuário na oferta concluída';
$string['event:offer_user_enrol_failed'] = 'Matrícula do usuário na oferta reprovada';
$string['event:offer_user_enrol_not_completed'] = 'Matrícula do usuário na oferta não concluída';
$string['event:offer_user_enrol_abandoned'] = 'Matrícula do usuário na oferta abandonada';
$string['event:offer_user_enrol_created'] = 'Matrícula de usuário criada';
$string['event:offer_user_enrol_edited'] = 'Matrícula do usuário editada';
$string['event:offer_user_enrol_deleted'] = 'Matrícula do usuário excluída';
$string['event:user_enrolment_reenroled'] = 'Rematrícula do usuário em turma';

$string['message:class_created'] = 'Turma {$a->name} criada com sucesso.';
$string['message:enrolment_success'] = 'Usuário matriculado na turma com sucesso';
$string['message:class_updated'] = 'Turma {$a->name} atualizada com sucesso.';
$string['message:class_deleted'] = 'Turma {$a->name} excluída com sucesso.';
$string['message:enrolment_failed'] = 'Erro ao matricular usuário';
$string['message:enrolment_failed_detailed'] = 'Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.';
$string['message:self_unenrol'] = 'Ao cancelar a matrícula, seu progresso até aqui será salvo e você não terá mais acesso ao conteúdo do curso.';
$string['message:cancel_reason'] = 'Justificativa do cancelamento';
$string['message:cancel_reason_help'] = 'Digite aqui a justificativa do cancelamento da matrícula';
$string['message:offer_user_enrol_self_canceled'] = "Sua inscrição na turma foi cancelada com sucesso.</br> Aguarde que você será redirecionado para a página principal.";
$string['message:self_unenrol_success'] = 'Sua matrícula foi cancelada com sucesso.';
$string['message:reason_cancel_previous_enrolment'] = 'O cancelamento da matrícula na turma foi realizado porque o usuário {$a->adminid} matriculou o usuário {$a->userid} na turma {$a->newofferclassid}.';

$string['error:user_not_found'] = 'Usuário com ID {$a} não encontrado';
$string['error:offer_class_not_found'] = 'Turma da oferta não encontrada';
$string['error:enrol_instance_not_found'] = 'Instância de matrícula não encontrada';
$string['error:user_already_enrolled'] = 'Usuário {$a} já está matriculado nesta turma';
$string['error:accessdenied'] = 'Você não tem permissão para acessar o plugin ' . $string['pluginname'];
$string['error:cannot_activate_offer'] = 'Não foi possível ativar a oferta. Certifique-se de que todos os cursos têm turmas configuradas e pelo menos um público-alvo atribuído.';
$string['error:offer_already_inactive'] = 'A oferta já está inativa';
$string['error:offer_already_active'] = 'A oferta já está ativa';
$string['error:cannot_delete_offer'] = 'Esta oferta não pode ser excluída.';
$string['error:cannot_delete_offer_course'] = 'Este curso não pode ser removido da oferta.';
$string['error:cannot_created_offer_class'] = 'A turma não pôde ser criada';
$string['error:cannot_delete_offer_class'] = 'Esta turma não pode ser removida da oferta';
$string['error:duplicate_offer_course'] = 'A combinação de oferta e curso já existe.';
$string['error:duplicate_offer_audience'] = 'A combinação de oferta e público-alvo já existe.';
$string['error:offer_audience_not_found'] = 'A relação entre oferta e público-alvo não foi encontrada.';
$string['error:audience_not_found'] = 'O público-alvo não foi encontrado.';
$string['error:invalid_audience_ids'] = 'A lista de públicos-alvo enviada não é válida.';
$string['error:offer_course_not_found'] = 'A relação entre oferta e curso não foi encontrada.';
$string['error:offer_course_already_active'] = 'A relação entre oferta e curso já está ativada';
$string['error:offer_course_already_inactive'] = 'A relação entre oferta e curso já está desativada';
$string['error:offer_class_already_active'] = 'A turma já está ativada';
$string['error:offer_class_already_inactive'] = 'A turma já está desativada';
$string['error:course_not_found'] = 'Curso não encontrado.';
$string['error:enrol_plugin_not_found'] = 'Método de registro não encontrado.';
$string['error:offer_not_found'] = 'Oferta não encontrada';
$string['error:class_not_found'] = 'Turma não encontrada';
$string['error:user_enrol_not_found'] = 'Matrícula do usuário não encontrada';
$string['error:invalid_situation'] = 'Situação de matrícula inválida';
$string['error:offer_name_required'] = 'Nome da oferta é obrigatório';
$string['error:offer_status_required'] = 'Status da oferta é obrigatório';
$string['error:enrolid_doesnt_exist'] = 'A instância de matrícula não existe';
$string['error:enrolid_already_exists'] = 'A instância de matrícula já tem uma relação com um curso em uma oferta';
$string['error:offer_class_already_finished'] = 'A turma já foi finalizada';
$string['error:offer_class_already_started'] = 'A turma com id {$a} já foi iniciada';
$string['error:cannot_view_course'] = 'Você não pode visualizar este curso';
$string['error:user_not_enrolled'] = 'Usuário não está matriculado nesta turma';
$string['error:invalid_userid'] = 'ID de usuário inválido';
$string['error:role_not_found'] = 'Papel de professor não encontrado';
$string['error:update_teacher_error'] = 'Ocorreu um erro ao atualizar os professores';
$string['error:cannot_update_offer_class'] = 'Não foi possível atualizar a turma';
$string['error:duplicate_different_offer'] = 'A duplicação só é permitida entre cursos da mesma oferta.';
$string['error:duplicate_same_course'] = 'A duplicação não pode ocorrer no mesmo curso da turma original.';
$string['error:cannot_create_enrol_instance'] = 'Não foi possível criar a nova instância de matrícula no curso de destino.';
$string['error:classname_required'] = 'O nome da turma é obrigatório.';
$string['error:startdate_required'] = 'A data de início da turma é obrigatória.';
$string['error:offercourseid_required'] = 'O ID do curso da oferta é obrigatório.';
$string['error:minusers_numeric'] = 'O número mínimo de usuários deve ser um número.';
$string['error:maxusers_numeric'] = 'O número máximo de usuários deve ser um número.';
$string['error:enddate_required'] = 'A data de fim da turma é obrigatória.';
$string['error:enddate_before_startdate'] = 'A data de fim da turma deve ser posterior à data de início.';
$string['error:preregistrationstartdate_required'] = 'A data de início da pré-inscrição é obrigatória.';
$string['error:preregistrationenddate_required'] = 'A data de fim da pré-inscrição é obrigatória.';
$string['error:preregistrationenddate_before_preregistrationstartdate'] = 'A data de fim da pré-inscrição deve ser posterior à data de início da pré-inscrição.';
$string['error:maxusers_less_than_minusers'] = 'O número máximo de usuários deve ser maior ou igual ao número mínimo de usuários.';
$string['error:enrolperiod_numeric'] = 'O prazo de conclusão deve ser um número.';
$string['error:extensiondaysavailabie_required'] = 'O número de dias antes do término para prorrogação é obrigatório.';
$string['error:extensionmaxrequests_required'] = 'O número máximo de prorrogações permitidas é obrigatório.';
$string['error:extensionmaxrequests_numeric'] = 'O número máximo de prorrogações permitidas deve ser um número.';
$string['error:field_not_mapped'] = 'Campo não mapeado';
$string['error:user_enrol_not_canceled'] = 'Matrícula não foi cancelada';
$string['error:cancel_reason_cannot_be_empty'] = 'A justificativa do cancelamento não pode estar vazia na matrícula';
$string['error:offerclass_not_active'] = 'Turma não está ativa';
$string['error:offercourse_not_active'] = 'Curso na Oferta não está ativo';
$string['error:offer_not_active'] = 'Oferta não está ativa';
$string['error:reached_max_users'] = 'Número máximo de matrículas atingido.';
$string['error:outside_preenrolment'] = 'Fora do período de pré-inscrição';
$string['error:outside_classperiod'] = 'Fora do período da turma';
$string['error:preenrol_starts_on'] = 'As matrículas nesta turma não estão disponíveis. Estão programadas para começar em {$a->startdate}.';
$string['error:preenrol_starts_between'] = 'As matrículas nesta turma não estão disponíveis. Estão programadas para abrir em {$a->startdate} e fechar em {$a->enddate}.';
$string['error:user_has_active_enrolment'] = 'O usuário {$a} já possui uma matrícula ativa neste curso através de uma das turmas';
$string['error:operational_cycle_notfound'] = 'Turma com id {$a} não encontrada. Ignorando task.';
$string['error:operational_cycle_fail'] = 'Falha ao atualizar ciclo operacional. Abortando.';
$string['error:invalid_situation_transition'] = 'Transição de situação inválida de "{$a->current}" para "{$a->new}"';
$string['error:formdata_missing'] = 'Dados do formulário estão faltando.';
$string['error:invalid_offer_user_enrol_for_cancel'] = 'Matrícula do usuário na oferta inválida para cancelamento.';
$string['error:cannot_cancel_offer_user_enrol'] = 'Não foi possível cancelar a matrícula do usuário na oferta.';
$string['error:self_unenrol_failed'] = 'Ocorreu um erro ao tentar cancelar sua matrícula. Por favor, tente novamente ou entre em contato com o suporte.';
$string['error:delete_user_enrolment_fail'] =  'Falha ao excluir user_enrolments';
$string['invaliddateformat'] = 'Formato de data inválido: {$a}. Por favor, use o formato AAAA-MM-DD.';
$string['error:invalid_orderby_field'] = 'Valor orderby inválido: {$a}';

$string['config:enableplugin'] = 'Habilitar plugin';
$string['config:enableplugin_desc'] = 'Habilita/desabilita o plugin no ambiente. Ao desabilitar, todas as turmas das ofertas criadas serão inativadas.';
$string['config:enableplatformenrol'] = 'Habilitar métodos de registro da plataforma';
$string['config:enableplatformenrol_desc'] = 'Ao desabilitar esta opção, todos os métodos de registro disponíveis na plataforma serão desabilitados, deixando disponíveis apenas os métodos do Gerenciador de Ofertas.';
$string['config:enabletypeoptions'] = 'Habilitar filtro por Tipo de Oferta';
$string['config:enabletypeoptions_desc'] = 'Habilita um filtro por Tipo de Oferta na página inicial do Gerenciador de Ofertas.';
$string['config:typeoptions_help'] = 'Ao habilitar esta opção, um campo de escolha será criado dentro da instância da oferta, contendo as opções definidas no editor de texto abaixo. Essa configuração não vincula regras específicas, mas auxilia na identificação do tipo de oferta. Além disso, também adiciona um filtro chamado \'Tipo de Oferta\' na página principal do Gerenciador de Ofertas';
$string['config:optionsmenu'] = 'Tipos de Oferta';
$string['config:typeoptions'] = 'Opções de Filtro por Tipo de Oferta';
$string['config:typeoptions_desc'] = 'Insira os valores que serão exibidos no filtro de Tipo de Oferta, um por linha.';
$string['config:defaulttypeoption'] = 'Valor padrão';
$string['config:defaulttypeoption_desc'] = 'Valor padrão a ser exibido no campo Tipo de Oferta.';

$string['task:turnclassesaccessible'] = 'Tornar turmas acessíveis automaticamente';
$string['task:syncenrolplugins'] = 'Sincronizar plugins de matrícula dependentes';
$string['task:operational_cycle_start'] = 'Executando atualização do ciclo operacional para a turma {$a->id} (tentativa {$a->attempt}).';
$string['task:operational_cycle_success'] = 'Ciclo operacional da turma atualizado com sucesso.';
$string['task:operational_cycle_retry'] = 'Falha ao atualizar ciclo operacional. Nova tentativa em 10 minutos (tentativa {$a->attempt}/{$a->max}).';

$string['page:self_unenrol_title'] = 'Cancelar Matrícula';
$string['page:self_unenrol_heading'] = 'Cancelar Matrícula na turma {$a}';
$string['confirm:self_unenrol'] = 'Tem certeza que deseja cancelar sua matrícula? Você perderá acesso ao conteúdo do curso.';

// Strings de rematrícula
$string['reenrolme'] = 'Rematrícula na turma {$a->classname}';
$string['reenrol_info'] = 'Ao rematricular-se, o progresso do usuário dentro do curso será migrado para o histórico e os dados de progresso serão apagados.';
$string['reenrolme_button'] = 'Rematricular-me';
$string['error:cannot_reenrol'] = 'Você não pode se matricular nesta turma';
$string['error:method_not_implemented'] = 'Erro: O método requerido \'{$a}\' não está implementado na classe que usa o trait.';
$string['error:enrol_already_archived'] = 'O registro de matrícula do usuário já está arquivado.';
$string['error:cannot_create_history_record'] = 'Não foi possível criar o registro histórico para a matrícula do usuário.';
$string['error:user_enrolment_not_found'] = 'Registro de matrícula do usuário não encontrado ou ID do usuário está faltando.';
$string['message:reenrolment_success'] = 'Você foi rematriculado no curso {$a}!';

// Strings de prorrogação
$string['extendenrol'] = 'Prorrogar matrícula';
$string['nopermissions'] = 'Você não tem permissões para realizar esta ação.';
$string['error:invalid_offer_user_enrol'] = 'Matrícula do usuário na oferta inválida.';
$string['enableextension'] = 'Habilitar Prorrogação';
$string['enableextension_desc'] = 'Habilita a funcionalidade de prorrogação de matrícula para esta turma.';
$string['extensionperiod'] = 'Período de Prorrogação (dias)';
$string['extensionperiod_desc'] = 'Número de dias a serem adicionados à data de término da matrícula do usuário ao prorrogar.';
$string['extensiondaysavailable'] = 'Dias antes da data final para mostrar opção de prorrogação';
$string['extensiondaysavailable_desc'] = 'Número de dias antes da data final da matrícula que a opção de prorrogação será exibida ao usuário.';
$string['extensionmaxrequests'] = 'Máximo de Solicitações de Prorrogação';
$string['extensionmaxrequests_desc'] = 'Número máximo de vezes que um usuário pode solicitar prorrogação para esta matrícula.';
$string['extensionallowedsituations'] = 'Situações Permitidas para Prorrogação';
$string['extensionallowedsituations_desc'] = 'Selecione as situações de matrícula que estão permitidas a solicitar prorrogação.';

// Mensagens de erro de prorrogação
$string['error:extension_class_not_found'] = 'Prorrogação não possível: Turma não encontrada.';
$string['error:extension_invalid_timeend'] = 'Prorrogação não possível: Você já atingiu o limite de tempo para concluir o curso.';
$string['error:extension_invalid_config'] = 'Prorrogação não possível: A configuração de prorrogação está incompleta ou é inválida para esta turma.';
$string['error:extension_invalid_situation'] = 'Prorrogação não possível: Sua situação atual de matrícula ("{$a}") não permite prorrogação.';
$string['error:extension_limit_reached'] = 'Prorrogação não possível: Você atingiu o número máximo de prorrogações permitidas.';
$string['error:cannot_extend_enrol'] = 'Não foi possível prorrogar a matrícula';
$string['error:extension_cannot_process'] = 'Não foi possível processar a solicitação de prorrogação: {$a}';
$string['error:extension_invalid_period'] = 'Prorrogação não possível: O período de prorrogação é inválido ou não está configurado.';

// Mensagens de sucesso e aviso de prorrogação
$string['success:extension_granted'] = 'Sua matrícula foi prorrogada com sucesso.';
$string['warning:extension_limited_by_class_enddate'] = 'Sua matrícula foi prorrogada, mas foi limitada para coincidir com a data final da turma.';

$string['event:user_enrolment_extended'] = 'Matrícula do usuário prorrogada';

$string['extension_reason_default'] = 'Matrícula prorrogada por solicitação do usuário.';

$string['message:offer_user_enrol_self_extended'] = 'Sua matrícula foi prorrogada com sucesso.';
$string['error:cannot_extend_offer_user_enrol'] = 'Não foi possível prorrogar a matrícula do usuário na oferta.';

// Strings de formulário de prorrogação
$string['message:extend_enrol'] = 'Ao prorrogar sua matrícula, você terá mais tempo para concluir o curso. Por favor, confirme que deseja prorrogar sua matrícula.';
$string['message:extend_reason'] = 'Motivo da solicitação';
$string['message:extend_reason_help'] = 'Por favor, informe um motivo para solicitar a prorrogação.';
$string['message:extension_new_date'] = 'Se prorrogada, sua matrícula será válida até: <strong>{$a->date}</strong>';
$string['message:extension_limited_warning'] = '<strong>Observação:</strong> A prorrogação será limitada para coincidir com a data final da turma ({$a->date}).';
$string['message:extension_count'] = 'Esta será a prorrogação {$a->current} de {$a->max} permitidas.';

$string['enrolperiod'] = 'Prazo de conclusão';
$string['enrol_period_format'] = 'de {$a->startdate} à {$a->enddate}';
$string['enrol_period_format_start'] = 'Inicia em {$a->startdate}';
$string['class_format_start'] = 'Turma inicia em {$a->startdate}';

$string['startdate'] = 'Data de início da turma';