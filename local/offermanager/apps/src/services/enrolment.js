import { ajax } from '@/helpers/moodle';

/**
 * Busca matrículas de uma turma.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function fetchEnrolments(params = {}) {
  try {
    const response = await ajax('local_offermanager_fetch_enrolments', {
      offerclassid: params.offerclassid,
      userids: params.userids || [],
      page: params.page || 1,
      perpage: params.perpage || 20,
      orderby: params.orderby || 'fullname',
      direction: params.direction || 'ASC'
    });

    if(response.error) {
      throw new Error(error.message || 'Erro ao buscar matrículas')
    }

    return response

  } catch (error) {
    throw new Error(error.message || 'Erro ao buscar matrículas')
  }
}

/**
 * Busca usuários matriculados em uma turma com filtros.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function searchEnrolledUsers(params = {}) {
  try {
    const response = await ajax('local_offermanager_get_enroled_users', {
      offerclassid: params.offerclassid,
      searchstring: params.searchstring || '',
      fieldstring: params.fieldstring || 'name',
      excludeduserids: params.excludeduserids || [],
    });

    if (response.error) {
      throw new Error(response.message || 'Erro ao buscar opções de filtro')

    }
    return response;
  } catch (error) {
      throw new Error(response.message || 'Erro ao buscar opções de filtro')
  }
}

/**
 * Matricula usuários em uma turma.
 * @param {object} params
 * @returns {Promise<Array|Object>} Array de resultados de matrícula ou objeto com propriedade data
 */
export async function enrolUsers(params = {}) {
  try {
    const response = await ajax('local_offermanager_enrol_users', {
      offerclassid: params.offerclassid,
      userids: params.userids || [],
      roleid: params.roleid || 5,
    });

    if (response.error) {
      throw new Error(response.message || 'Erro ao matricular usuários')
    }

    return response;
  } catch (error) {
    throw new Error(response.message || 'Erro ao matricular usuários');
  }
}

/**
 * Busca usuários potenciais para matrícula em uma turma.
 * @param {number} offerclassid ID da turma
 * @returns {Promise<Array>} Array de usuários potenciais
 */
export async function getPotentialUsersToEnrol(offerclassid, searchString = '', excludedUserids) {
  
  const response = await ajax('local_offermanager_get_potential_users_to_enrol', {
    offerclassid: offerclassid,
    search_string: searchString,
    excluded_userids: excludedUserids
  });

  if (response.error) {
    console.error('Erro na resposta de getPotentialUsersToEnrol:', response.error);
    return [];
  }

  return response;
}

/**
 * Edita a matrícula de um usuário.
 * @param {object} params Parâmetros para edição da matrícula
 * @param {number} params.offeruserenrolid ID da matrícula do usuário
 * @param {number} params.status Status da matrícula (0=inativo, 1=ativo)
 * @param {number} params.timestart Timestamp de início da matrícula
 * @param {number} params.timeend Timestamp de fim da matrícula
 * @param {number} params.roleid ID do papel do usuário
 * @returns {Promise<boolean>} Indica se a edição foi bem-sucedida
 */
export async function editEnrolment(params = {}) {
  try {
    const response = await ajax('local_offermanager_edit_offer_user_enrol', {
      offeruserenrolid: params.offeruserenrolid,
      status: params.status,
      timestart: params.timestart,
      timeend: params.timeend,
      roleid: params.roleid
    });

    // Verificar se a resposta é válida
    if (response === undefined || response === null) {
      console.error('Resposta vazia de editEnrolment');
      return false;
    }

    // Se a resposta for um booleano, retornar diretamente
    if (typeof response === 'boolean') {
      return response;
    }

    // Se a resposta for um objeto com propriedade success
    if (response && typeof response.success === 'boolean') {
      return response.success;
    }

    // Se a resposta for um objeto com formato { error: false, data: ... }
    if (response && response.error === false) {
      // Se data for false, significa que ocorreu algum erro na operação
      // Se data for true, a operação foi bem-sucedida
      return response.data === true;
    }

    // Se chegou aqui, a resposta tem um formato não reconhecido
    console.warn('Formato de resposta não reconhecido:', response);
    return false;
  } catch (error) {
    console.error('Erro ao editar matrícula:', error);
    return false;
  }
}

/**
 * Exclui a matrícula de um usuário.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @returns {Promise<boolean>} Indica se a exclusão foi bem-sucedida
 */
export async function deleteEnrolment(offeruserenrolid) {
  try {
    // Não vamos mais usar o método individual, apenas o método em lote
    // Isso evita o erro de tipo de retorno incompatível
    return await deleteEnrolmentBulk([offeruserenrolid])
      .then(results => {
        // Se os resultados forem um array e o primeiro item tem operation_status true
        if (Array.isArray(results) && results.length > 0 && results[0].operation_status) {
          return true;
        }

        // Se os resultados forem um array vazio, verificar se a operação foi bem-sucedida
        // através da resposta { error: false, data: true }
        if (results && results.error === false && results.data === true) {
          return true;
        }

        return false;
      });
  } catch (error) {
    console.error('Erro ao excluir matrícula:', error);
    return false;
  }
}

/**
 * Edita matrículas em lote.
 * @param {object} params Parâmetros para edição das matrículas
 * @param {Array<number>} params.offeruserenrolids Array de IDs das matrículas dos usuários
 * @param {number} params.status Status da matrícula (0=inativo, 1=ativo)
 * @param {number} params.timestart Timestamp de início da matrícula
 * @param {number} params.timeend Timestamp de fim da matrícula
 * @returns {Promise<Array>} Array com os resultados das edições
 */
export async function editEnrolmentBulk(params = {}) {
  try {
    const response = await ajax('local_offermanager_edit_offer_user_enrol_bulk', {
      offeruserenrolids: params.offeruserenrolids || [],
      status: params.status,
      timestart: params.timestart,
      timeend: params.timeend
    });
    // Se a resposta for { error: false, data: true }, significa que todas as edições foram bem-sucedidas
    if (response && response.error === false && response.data === true) {
      // Criar um array de resultados com status de sucesso para todas as matrículas
      const results = params.offeruserenrolids.map(id => ({
        id: id,
        operation_status: true
      }));

      return results;
    }

    // Se a resposta for um array, retornar diretamente
    if (Array.isArray(response)) {
      return response;
    }

    // Se a resposta for um objeto com propriedade data que é um array
    if (response && Array.isArray(response.data)) {
      return response.data;
    }

    // Se não conseguimos interpretar a resposta, retornar um array vazio
    console.error('Formato de resposta não reconhecido:', response);
    return [];
  } catch (error) {
    console.error('Erro ao editar matrículas em lote:', error);
    throw error;
  }
}

/**
 * Exclui matrículas em lote.
 * @param {Array<number>} offeruserenrolids Array de IDs das matrículas dos usuários
 * @returns {Promise<Array>} Array com os resultados das exclusões
 */
export async function deleteEnrolmentBulk(offeruserenrolids) {
  try {
    // Chamar o webservice diretamente
    const response = await ajax('local_offermanager_delete_offer_user_enrol_bulk', {
      offeruserenrolids: offeruserenrolids
    });
    // Se a resposta for { error: false, data: true }, significa que todas as exclusões foram bem-sucedidas
    if (response && response.error === false && response.data === true) {
      // Criar um array de resultados com status de sucesso para todas as matrículas
      const results = offeruserenrolids.map(id => ({
        id: id,
        operation_status: true
      }));

      return results;
    }

    // Se a resposta for um array, retornar diretamente
    if (Array.isArray(response)) {
      return response;
    }

    // Se a resposta for um objeto com propriedade data que é um array
    if (response && Array.isArray(response.data)) {
      return response.data;
    }

    // Se chegou aqui, a resposta tem um formato não reconhecido
    console.warn('Formato de resposta não reconhecido:', response);
    return [];
  } catch (error) {
    console.error('Erro ao excluir matrículas em lote:', error);
    return [];
  }
}

/**
 * Busca os papéis de um usuário específico em uma turma.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @returns {Promise<Array>} Array de papéis do usuário
 */
export async function getUserRoles(offeruserenrolid) {
  try {
    const response = await ajax('local_offermanager_get_roles', {
      offeruserenrolid: offeruserenrolid
    });

    // Verificar se a resposta é válida
    if (!response) {
      console.error('Resposta vazia de getUserRoles');
      return [];
    }

    // Se a resposta contiver um erro, log e retorna uma estrutura vazia
    if (response.error) {
      console.error('Erro na resposta de getUserRoles:', response.error);
      return [];
    }

    // Verificar se a resposta é um array
    if (Array.isArray(response)) {
      return response;
    }

    // Se a resposta for um objeto com propriedade data que é um array
    if (response && Array.isArray(response.data)) {
      return response.data;
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar papéis do usuário:', error);
    return [];
  }
}

/**
 * Atualiza os papéis de um usuário em uma turma.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @param {Array<number>} roleids Array de IDs dos papéis
 * @returns {Promise<boolean>} Indica se a atualização foi bem-sucedida
 */
export async function updateUserRoles(offeruserenrolid, roleids) {
  try {
    const response = await ajax('local_offermanager_update_roles', {
      offeruserenrolid: offeruserenrolid,
      roleids: Array.isArray(roleids) ? roleids : [roleids]
    });

    // Verificar se a resposta é válida
    if (response === undefined || response === null) {
      console.error('Resposta vazia de updateUserRoles');
      return false;
    }

    // Se a resposta for um booleano, retornar diretamente
    if (typeof response === 'boolean') {
      return response;
    }

    // Se a resposta for um objeto com propriedade success
    if (response && typeof response.success === 'boolean') {
      return response.success;
    }

    // Se a resposta for um objeto com formato { error: false, data: ... }
    if (response && response.error === false) {
      return response.data === true;
    }

    // Se chegou aqui, a resposta tem um formato não reconhecido
    console.warn('Formato de resposta não reconhecido:', response);
    return false;
  } catch (error) {
    console.error('Erro ao atualizar papéis do usuário:', error);
    return false;
  }
}
